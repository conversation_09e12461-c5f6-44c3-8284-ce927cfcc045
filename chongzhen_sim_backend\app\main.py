"""
FastAPI 启动入口
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.config import settings
from app.api import user_routes, game_routes, npc_routes, admin_routes
from app.db.database import init_db, close_mongo_connection
from app.utils.logger import setup_logger

# 初始化日志
logger = setup_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="崇祯模拟器 API",
    description="基于LLM的历史模拟游戏后端",
    version="0.1.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(user_routes.router, prefix="/api/user", tags=["用户"])
app.include_router(game_routes.router, prefix="/api/game", tags=["游戏"])
app.include_router(npc_routes.router, prefix="/api", tags=["NPC对话"])
app.include_router(admin_routes.router, prefix="/api/admin", tags=["管理"])

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    logger.info("崇祯模拟器后端启动中...")
    await init_db()
    logger.info("数据库初始化完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理"""
    logger.info("崇祯模拟器后端关闭")
    await close_mongo_connection()

@app.get("/")
async def root():
    """根路径"""
    return {"message": "崇祯模拟器后端 API", "version": "0.1.0"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )
