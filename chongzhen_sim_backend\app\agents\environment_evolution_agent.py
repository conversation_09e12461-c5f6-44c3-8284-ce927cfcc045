"""
环境演化Agent - 处理环境的自然演化和推演
"""

import json
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.agents.base_agent import BaseAgent, AgentInput, AgentOutput
# 临时定义结果类
class MinisterActionResult:
    def __init__(self, minister_id="", minister_name="", success=False, **kwargs):
        self.minister_id = minister_id
        self.minister_name = minister_name
        self.success = success
        self.memory_updates = kwargs.get('memory_updates', [])
        self.external_actions = kwargs.get('external_actions', [])
        self.resource_changes = kwargs.get('resource_changes', {})
        self.error_message = kwargs.get('error_message', "")

class EnvironmentActionResult:
    def __init__(self, success=False, **kwargs):
        self.success = success
        self.error_message = kwargs.get('error_message', "")
        self.execution_time_seconds = kwargs.get('execution_time_seconds', 0)
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class EnvironmentEvolutionInput(AgentInput):
    """环境演化输入"""
    edict_content: str
    minister_action_results: List[Dict[str, Any]]  # 改为字典列表
    current_empire_state: Dict[str, Any]
    current_regions_state: List[Dict[str, Any]]
    current_turn: int
    current_year: int
    recent_events: List[Dict[str, Any]] = []

class EnvironmentEvolutionOutput(BaseModel):
    """环境演化输出"""
    # 推演结果
    edict_effects: Dict[str, Any] = {}
    minister_action_effects: Dict[str, Any] = {}
    natural_evolution_effects: Dict[str, Any] = {}
    
    # 数据变化
    empire_changes: Dict[str, float] = {}
    region_changes: Dict[str, Dict[str, float]] = {}
    
    # 生成的事件
    new_events: List[Dict[str, Any]] = []
    crisis_events: List[Dict[str, Any]] = []
    
    # 推理过程
    reasoning: str = ""

class EnvironmentEvolutionAgent(BaseAgent):
    """环境演化Agent"""
    
    def get_system_prompt(self) -> str:
        return """你是明朝帝国环境演化模拟系统，负责推演帝国各方面的变化。

你的职责：
1. 基于皇帝诏书内容，推演对帝国各项数据的影响
2. 基于大臣行动结果，推演对帝国和地区的影响
3. 模拟自然环境演化（天灾、人口变化、经济波动等）
4. 生成新的历史事件和危机
5. 计算所有变化对帝国数据的综合影响

推演要素：

A. 诏书推演：
- 分析诏书内容的政策方向和执行难度
- 评估对国库、军队、民心、稳定度的影响
- 考虑政策的短期和长期效果
- 评估执行过程中可能的阻力和副作用

B. 大臣行动推演：
- 分析每个大臣行动的直接效果
- 评估大臣间行动的协同或冲突效应
- 计算资源调配对各地区的影响
- 评估政治派系活动对朝廷稳定的影响

C. 自然演化：
- 人口自然增长/减少
- 经济周期性波动
- 天气和自然灾害
- 疾病传播
- 技术和文化发展

D. 危机生成：
- 基于当前局势生成合理的危机事件
- 考虑历史背景（如后金威胁、农民起义等）
- 评估危机的严重程度和影响范围

输出格式为JSON：
{
    "edict_effects": {
        "treasury_change": -5000,
        "stability_change": 0.1,
        "military_morale_change": 0.05,
        "description": "诏书执行效果描述"
    },
    "minister_action_effects": {
        "total_resource_allocated": 10000,
        "administrative_efficiency": 0.02,
        "description": "大臣行动综合效果"
    },
    "natural_evolution_effects": {
        "population_growth": 50000,
        "economic_fluctuation": -0.01,
        "natural_disasters": [],
        "description": "自然演化效果"
    },
    "empire_changes": {
        "treasury": -3000,
        "food_storage": 5000,
        "total_military": 1000,
        "population": 45000,
        "internal_stability": 0.08,
        "military_morale": 0.03
    },
    "region_changes": {
        "北京": {"population": 1000, "stability": 0.05, "prosperity": 0.02},
        "辽东": {"population": -500, "stability": -0.1, "military": 500}
    },
    "new_events": [
        {
            "type": "political",
            "title": "事件标题",
            "description": "事件描述",
            "severity": 3,
            "affected_regions": ["北京"],
            "effects": {"stability": -0.05}
        }
    ],
    "crisis_events": [
        {
            "type": "military",
            "title": "危机标题", 
            "description": "危机描述",
            "severity": 5,
            "affected_regions": ["辽东"],
            "required_response": "需要的应对措施"
        }
    ],
    "reasoning": "详细的推演过程和逻辑"
}"""

    async def process(self, input_data: EnvironmentEvolutionInput) -> AgentOutput:
        """处理环境演化"""
        try:
            logger.info(f"开始环境演化推演，回合: {input_data.current_turn}")
            
            # 构建提示词
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": self._build_evolution_prompt(input_data)}
            ]
            
            # 调用LLM
            response = await self.call_llm(messages, temperature=0.7, max_tokens=3000)
            
            # 解析响应
            try:
                evolution_data = json.loads(response)
                result = EnvironmentEvolutionOutput(**evolution_data)
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"解析环境演化结果失败: {e}")
                # 返回默认结果
                result = EnvironmentEvolutionOutput(
                    reasoning="解析失败，使用默认演化结果"
                )
            
            logger.info(f"环境演化推演完成，生成 {len(result.new_events)} 个新事件")
            
            return AgentOutput(
                success=True,
                result=result,
                metadata={
                    "events_generated": len(result.new_events),
                    "crises_generated": len(result.crisis_events),
                    "regions_affected": len(result.region_changes)
                }
            )
            
        except Exception as e:
            logger.error(f"环境演化推演失败: {e}")
            return AgentOutput(
                success=False,
                result=None,
                error_message=str(e)
            )
    
    def _build_evolution_prompt(self, input_data: EnvironmentEvolutionInput) -> str:
        """构建演化提示词"""
        prompt = f"""
请基于以下信息进行帝国环境演化推演：

=== 皇帝诏书 ===
{input_data.edict_content}

=== 大臣行动结果 ===
{self._format_minister_actions(input_data.minister_action_results)}

=== 当前帝国状态 ===
{self._format_empire_state(input_data.current_empire_state)}

=== 各地区状态 ===
{self._format_regions_state(input_data.current_regions_state)}

=== 近期事件 ===
{self._format_recent_events(input_data.recent_events)}

=== 时间背景 ===
崇祯{input_data.current_year}年，第{input_data.current_turn}回合
历史背景：明朝末期，面临内忧外患，后金威胁，农民起义频发

请进行全面的环境演化推演，包括：
1. 诏书执行对帝国的具体影响
2. 大臣行动的综合效果
3. 自然环境的演化变化
4. 可能发生的新事件和危机
5. 对各项数据的具体数值变化

注意：
- 变化要符合历史逻辑和现实规律
- 考虑政策执行的时间延迟和阻力
- 平衡正面和负面影响
- 数值变化要合理，避免过于极端
"""
        return prompt
    
    def _format_minister_actions(self, actions: List[MinisterActionResult]) -> str:
        """格式化大臣行动结果"""
        if not actions:
            return "本回合无大臣行动"
        
        formatted = []
        for action in actions:
            if action.success:
                formatted.append(f"""
大臣：{action.minister_name}
- 记忆更新：{len(action.memory_updates)}项
- 对外行动：{len(action.external_actions)}项
- 资源调配：{sum(action.resource_changes.values())}两银子
""")
            else:
                formatted.append(f"大臣：{action.minister_name} - 行动失败：{action.error_message}")
        
        return "\n".join(formatted)
    
    def _format_empire_state(self, state: Dict[str, Any]) -> str:
        """格式化帝国状态"""
        return f"""
国库：{state.get('treasury', 0)}两银子
粮食储备：{state.get('food_storage', 0)}石
军队总数：{state.get('total_military', 0)}人
总人口：{state.get('population', 0)}人
内政稳定度：{state.get('internal_stability', 0.5):.2f}
军心士气：{state.get('military_morale', 0.5):.2f}
对外关系：{state.get('foreign_relations', '一般')}
"""
    
    def _format_regions_state(self, regions: List[Dict[str, Any]]) -> str:
        """格式化地区状态"""
        if not regions:
            return "无地区数据"
        
        formatted = []
        for region in regions[:5]:  # 只显示前5个地区
            formatted.append(f"""
{region.get('name', '未知地区')}：
- 人口：{region.get('population', 0)}人
- 稳定度：{region.get('stability', 0.5):.2f}
- 繁荣度：{region.get('prosperity', 0.5):.2f}
- 忠诚度：{region.get('loyalty', 0.5):.2f}
""")
        
        return "\n".join(formatted)
    
    def _format_recent_events(self, events: List[Dict[str, Any]]) -> str:
        """格式化近期事件"""
        if not events:
            return "近期无重大事件"
        
        formatted = []
        for event in events[-3:]:  # 只显示最近3个事件
            formatted.append(f"- {event.get('title', '未知事件')}: {event.get('description', '无描述')}")
        
        return "\n".join(formatted)
