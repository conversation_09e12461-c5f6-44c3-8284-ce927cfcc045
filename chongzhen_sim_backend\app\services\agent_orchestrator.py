"""
Agent编排器 - 协调多个Agent的执行流程
"""

from typing import Dict, Any, List
from app.agents.edict_refine_agent import EdictRefineAgent, EdictRefineInput
from app.agents.simulate_agent import SimulateAgent, SimulateInput
from app.agents.story_progress_agent import StoryProgressAgent, StoryProgressInput
from app.agents.data_summary_agent import DataSummaryAgent, DataSummaryInput
from app.agents.output_generation_agent import OutputGenerationAgent, OutputGenerationInput
from app.agents.roleplay_agent import RoleplayAgent, RoleplayInput
from app.schemas.turn import TurnResult
from app.services.model_caller import ModelCaller
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class AgentOrchestrator:
    """Agent编排器"""
    
    def __init__(self):
        self.model_caller = ModelCaller()
        
        # 初始化所有Agent
        self.edict_refine_agent = EdictRefineAgent(self.model_caller)
        self.simulate_agent = SimulateAgent(self.model_caller)
        self.story_progress_agent = StoryProgressAgent(self.model_caller)
        self.data_summary_agent = DataSummaryAgent(self.model_caller)
        self.output_generation_agent = OutputGenerationAgent(self.model_caller)
        self.roleplay_agent = RoleplayAgent(self.model_caller)
    
    async def execute_full_turn(
        self,
        player_id: int,
        edict_content: str,
        current_state: Dict[str, Any],
        current_turn: int,
        current_year: int
    ) -> TurnResult:
        """执行完整的回合流程"""
        
        try:
            logger.info(f"开始执行完整回合流程，玩家ID: {player_id}")
            
            # 第一阶段：推演玩家指令
            logger.info("阶段1: 推演玩家指令")
            simulate_input = SimulateInput(
                player_id=player_id,
                edict_content=edict_content,
                current_empire_state=current_state,
                current_turn=current_turn,
                current_year=current_year
            )
            
            simulate_result = await self.simulate_agent.process(simulate_input)
            if not simulate_result.success:
                raise Exception(f"指令推演失败: {simulate_result.error_message}")
            
            simulation_output = simulate_result.result
            
            # 第二阶段：世界自演
            logger.info("阶段2: 世界自演")
            story_input = StoryProgressInput(
                player_id=player_id,
                current_empire_state=current_state,
                current_turn=current_turn,
                current_year=current_year,
                recent_events=[]  # TODO: 从数据库获取近期事件
            )
            
            story_result = await self.story_progress_agent.process(story_input)
            if not story_result.success:
                logger.warning(f"世界自演失败: {story_result.error_message}")
                story_output = None
            else:
                story_output = story_result.result
            
            # 第三阶段：数值总结
            logger.info("阶段3: 数值总结")
            data_input = DataSummaryInput(
                player_id=player_id,
                simulation_result=simulation_output.narrative,
                world_events=story_output.narrative if story_output else "",
                current_data=current_state
            )
            
            data_result = await self.data_summary_agent.process(data_input)
            if not data_result.success:
                logger.warning(f"数值总结失败: {data_result.error_message}")
                data_changes = {}
            else:
                data_changes = self._format_data_changes(data_result.result)
            
            # 第四阶段：输出生成
            logger.info("阶段4: 输出生成")
            output_input = OutputGenerationInput(
                player_id=player_id,
                simulation_narrative=simulation_output.narrative,
                world_narrative=story_output.narrative if story_output else "",
                data_changes=data_changes,
                current_turn=current_turn,
                current_year=current_year
            )
            
            output_result = await self.output_generation_agent.process(output_input)
            if not output_result.success:
                raise Exception(f"输出生成失败: {output_result.error_message}")
            
            final_output = output_result.result
            
            # 构建最终结果
            turn_result = TurnResult(
                success=True,
                title=final_output.title,
                content=final_output.main_content,
                data_changes=data_changes,
                next_turn_preview=final_output.next_turn_preview,
                is_game_over=False
            )
            
            logger.info(f"完整回合流程执行成功，玩家ID: {player_id}")
            return turn_result
            
        except Exception as e:
            logger.error(f"完整回合流程执行失败: {e}")
            # 返回错误结果
            return TurnResult(
                success=False,
                title="推演失败",
                content=f"本月推演出现问题：{str(e)}，请重试。",
                data_changes={},
                next_turn_preview="等待重新推演",
                is_game_over=False
            )
    
    async def refine_edict(self, edict_content: str, player_id: int) -> str:
        """润色诏书"""
        try:
            logger.info(f"开始润色诏书，玩家ID: {player_id}")
            
            refine_input = EdictRefineInput(
                player_id=player_id,
                raw_edict=edict_content,
                current_situation={}  # TODO: 获取当前情况
            )
            
            result = await self.edict_refine_agent.process(refine_input)
            if not result.success:
                logger.error(f"诏书润色失败: {result.error_message}")
                return edict_content  # 返回原始内容
            
            return result.result.refined_edict
            
        except Exception as e:
            logger.error(f"诏书润色异常: {e}")
            return edict_content
    
    async def roleplay_interaction(
        self,
        player_id: int,
        character_name: str,
        question: str,
        conversation_history: List[Dict[str, str]] = None
    ) -> str:
        """角色扮演交互"""
        try:
            logger.info(f"开始角色扮演，玩家ID: {player_id}, 角色: {character_name}")
            
            roleplay_input = RoleplayInput(
                player_id=player_id,
                character_name=character_name,
                player_question=question,
                current_situation={},  # TODO: 获取当前情况
                conversation_history=conversation_history or []
            )
            
            result = await self.roleplay_agent.process(roleplay_input)
            if not result.success:
                logger.error(f"角色扮演失败: {result.error_message}")
                return f"{character_name}：臣一时语塞，请皇上稍后再问。"
            
            # TODO: 更新角色忠诚度
            if result.result.loyalty_change != 0:
                await self._update_character_loyalty(
                    character_name, 
                    result.result.loyalty_change
                )
            
            return result.result.character_response
            
        except Exception as e:
            logger.error(f"角色扮演异常: {e}")
            return f"{character_name}：臣身体不适，恕难回答。"
    
    def _format_data_changes(self, data_summary_result) -> Dict[str, Any]:
        """格式化数据变化"""
        try:
            formatted_changes = {}
            
            # 合并所有类型的变化
            all_changes = []
            all_changes.extend(data_summary_result.financial_changes.items())
            all_changes.extend(data_summary_result.military_changes.items())
            all_changes.extend(data_summary_result.population_changes.items())
            all_changes.extend(data_summary_result.loyalty_changes.items())
            all_changes.extend(data_summary_result.resource_changes.items())
            
            for key, value in all_changes:
                formatted_changes[key] = {
                    "change": value,
                    "display": f"{value:+.1f}" if isinstance(value, float) else str(value)
                }
            
            return formatted_changes
            
        except Exception as e:
            logger.error(f"格式化数据变化失败: {e}")
            return {}
    
    async def _update_character_loyalty(self, character_name: str, loyalty_change: float):
        """更新角色忠诚度"""
        try:
            # TODO: 实现数据库更新逻辑
            logger.info(f"更新角色忠诚度: {character_name}, 变化: {loyalty_change}")
            
        except Exception as e:
            logger.error(f"更新角色忠诚度失败: {e}")
    
    async def batch_process_agents(
        self,
        agent_tasks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """批量处理Agent任务"""
        try:
            results = []
            
            for task in agent_tasks:
                agent_type = task.get("agent_type")
                input_data = task.get("input_data")
                
                if agent_type == "simulate":
                    result = await self.simulate_agent.process(input_data)
                elif agent_type == "story_progress":
                    result = await self.story_progress_agent.process(input_data)
                elif agent_type == "data_summary":
                    result = await self.data_summary_agent.process(input_data)
                elif agent_type == "output_generation":
                    result = await self.output_generation_agent.process(input_data)
                elif agent_type == "roleplay":
                    result = await self.roleplay_agent.process(input_data)
                else:
                    result = {"success": False, "error": f"未知Agent类型: {agent_type}"}
                
                results.append({
                    "task_id": task.get("task_id"),
                    "agent_type": agent_type,
                    "result": result
                })
            
            return results
            
        except Exception as e:
            logger.error(f"批量处理Agent任务失败: {e}")
            return []
