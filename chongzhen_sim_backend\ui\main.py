"""
崇祯模拟器 Streamlit 前端主界面
"""

import streamlit as st
import requests
import json
import random
import numpy as np
import time
from typing import Dict, List, Optional
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from logger import log_ui_event, ui_logger

# 导入配置
try:
    from config import (
        API_BASE_URL, UI_TITLE, UI_ICON, PAGES, COLORS,
        DEFAULT_REGIONS, DEFAULT_MINISTERS, EDICT_TEMPLATES, AI_SUGGESTIONS
    )
except ImportError:
    # 如果配置文件不存在，使用默认值
    API_BASE_URL = "http://127.0.0.1:8000"
    UI_TITLE = "崇祯模拟器"
    UI_ICON = "👑"
    PAGES = {}
    COLORS = {}
    DEFAULT_REGIONS = []
    DEFAULT_MINISTERS = []
    EDICT_TEMPLATES = {}
    AI_SUGGESTIONS = {}

# 页面配置
st.set_page_config(
    page_title=UI_TITLE,
    page_icon=UI_ICON,
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #8B4513;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    .section-header {
        font-size: 1.5rem;
        color: #CD853F;
        margin: 1rem 0;
        border-bottom: 2px solid #CD853F;
        padding-bottom: 0.5rem;
    }
    .emperor-card {
        background: linear-gradient(135deg, #FFD700, #FFA500);
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .minister-card {
        background: linear-gradient(135deg, #E6E6FA, #D8BFD8);
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        border-left: 4px solid #8B4513;
    }
    .region-card {
        background: linear-gradient(135deg, #F0F8FF, #E0F6FF);
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        border-left: 4px solid #4682B4;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #E3F2FD;
        border-left: 4px solid #2196F3;
    }
    .minister-message {
        background-color: #F3E5F5;
        border-left: 4px solid #9C27B0;
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """初始化会话状态"""
    print('🔄 初始化会话状态...')

    if 'player_id' not in st.session_state:
        st.session_state.player_id = None
        print('📝 初始化 player_id = None')
    else:
        print(f'👤 当前玩家ID: {st.session_state.player_id}')

    if 'current_page' not in st.session_state:
        st.session_state.current_page = "主界面"
        print('📄 初始化 current_page = 主界面')
    else:
        print(f'📄 当前页面: {st.session_state.current_page}')

    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
        print('💬 初始化聊天历史')

    if 'selected_minister' not in st.session_state:
        st.session_state.selected_minister = None
        print('👥 初始化选中大臣 = None')

    if 'edict_draft' not in st.session_state:
        st.session_state.edict_draft = ""
        print('📜 初始化诏书草稿')

def api_request(endpoint: str, method: str = "GET", data: Dict = None) -> Optional[Dict]:
    """发送API请求"""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        log_ui_event('api', f'请求: {method} {url}', data)

        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            if data:
                response = requests.post(url, json=data)
            else:
                response = requests.post(url)
        elif method == "PUT":
            if data:
                response = requests.put(url, json=data)
            else:
                response = requests.put(url)
        elif method == "DELETE":
            response = requests.delete(url)

        print(f'📥 响应状态码: {response.status_code}')

        if response.status_code == 200:
            response_data = response.json()
            print(f'✅ 响应成功: {len(str(response_data))} 字符')
            return response_data
        else:
            error_text = response.text
            print(f'❌ API请求失败: {response.status_code} - {error_text}')
            st.error(f"API请求失败: {response.status_code} - {error_text}")
            return None
    except requests.exceptions.ConnectionError as e:
        error_msg = f"无法连接到后端服务器 ({API_BASE_URL})"
        print(f'🔌 连接错误: {error_msg}')
        st.error(error_msg)
        st.info("请确保后端服务器正在运行在 http://127.0.0.1:8000")
        return None
    except requests.exceptions.Timeout as e:
        error_msg = "请求超时"
        print(f'⏰ 超时错误: {error_msg}')
        st.error(error_msg)
        return None
    except Exception as e:
        error_msg = f"请求异常: {str(e)}"
        print(f'💥 未知错误: {error_msg}')
        st.error(error_msg)
        return None

def login_page():
    """登录页面"""
    st.markdown('<h1 class="main-header">👑 崇祯模拟器 👑</h1>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown('<div class="section-header">登录游戏</div>', unsafe_allow_html=True)
        
        with st.form("login_form"):
            username = st.text_input("用户名", placeholder="请输入用户名")
            password = st.text_input("密码", type="password", placeholder="请输入密码")
            
            col_login, col_register = st.columns(2)
            
            with col_login:
                login_clicked = st.form_submit_button("登录", use_container_width=True)
            
            with col_register:
                register_clicked = st.form_submit_button("注册", use_container_width=True)
        
        if login_clicked and username and password:
            log_ui_event('auth', f'尝试登录用户: {username}')

            # 调用登录API - 使用查询参数
            login_data = api_request(f"/api/user/login?username={username}&password={password}", "POST")

            if login_data and "player_id" in login_data:
                player_id = login_data["player_id"]
                st.session_state.player_id = player_id
                log_ui_event('success', f'登录成功，玩家ID: {player_id}')
                st.success("登录成功！")
                st.rerun()
            else:
                print(f'❌ 登录失败，用户: {username}')
                st.error("登录失败，请检查用户名和密码")
        
        if register_clicked and username and password:
            log_ui_event('auth', f'尝试注册用户: {username}')

            # 调用注册API
            register_data = api_request("/api/user/register", "POST", {
                "username": username,
                "password": password
            })

            if register_data:
                print(f'✅ 注册成功，用户: {username}')
                st.success("注册成功！请登录")
            else:
                print(f'❌ 注册失败，用户: {username}')
                st.error("注册失败")

def main_interface():
    """主界面"""
    st.markdown('<h1 class="main-header">👑 崇祯模拟器 👑</h1>', unsafe_allow_html=True)
    
    # 侧边栏导航
    with st.sidebar:
        st.markdown("### 📋 功能菜单")
        
        if st.button("🏠 主界面", use_container_width=True):
            print('🏠 导航到主界面')
            st.session_state.current_page = "主界面"

        if st.button("👤 崇祯个人数据", use_container_width=True):
            print('👤 导航到个人数据页面')
            st.session_state.current_page = "个人数据"

        if st.button("🏛️ 大明势力数据", use_container_width=True):
            print('🏛️ 导航到势力数据页面')
            st.session_state.current_page = "势力数据"

        if st.button("🗺️ 地图", use_container_width=True):
            print('🗺️ 导航到地图页面')
            st.session_state.current_page = "地图"

        if st.button("👥 召见大臣", use_container_width=True):
            print('👥 导航到召见大臣页面')
            st.session_state.current_page = "召见大臣"

        if st.button("📜 诏书写作", use_container_width=True):
            print('📜 导航到诏书写作页面')
            st.session_state.current_page = "诏书写作"

        st.markdown("---")
        if st.button("🚪 退出登录", use_container_width=True):
            print('🚪 用户退出登录')
            st.session_state.player_id = None
            st.session_state.current_page = "主界面"
            st.rerun()
    
    # 根据当前页面显示内容
    current_page = st.session_state.current_page
    print(f'📄 渲染页面: {current_page}')

    if current_page == "主界面":
        show_dashboard()
    elif current_page == "个人数据":
        show_emperor_data()
    elif current_page == "势力数据":
        show_empire_data()
    elif current_page == "地图":
        show_map()
    elif current_page == "召见大臣":
        show_ministers()
    elif current_page == "诏书写作":
        show_edict_editor()
    else:
        print(f'⚠️ 未知页面: {current_page}')

def show_dashboard():
    """显示仪表板"""
    st.markdown('<div class="section-header">📊 游戏概览</div>', unsafe_allow_html=True)
    
    # 获取游戏状态
    game_status = api_request(f"/api/game/status/{st.session_state.player_id}")
    
    if game_status:
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("当前回合", game_status.get("current_turn", 1))
        
        with col2:
            st.metric("崇祯年", f"崇祯{game_status.get('current_year', 1)}年")
        
        with col3:
            st.metric("游戏状态", game_status.get("status", "进行中"))
        
        with col4:
            if st.button("⏭️ 推进回合", use_container_width=True):
                result = api_request(f"/api/game/turn/advance/{st.session_state.player_id}", "POST")
                if result:
                    st.success("回合推进成功！")
                    st.rerun()
    
    # 显示最近事件
    st.markdown('<div class="section-header">📰 最近事件</div>', unsafe_allow_html=True)
    events = api_request(f"/api/game/events/{st.session_state.player_id}")
    
    if events and events.get("events"):
        for event in events["events"]:
            st.info(f"📅 {event.get('date', '未知日期')}: {event.get('description', '无描述')}")
    else:
        st.info("暂无最新事件")

def show_emperor_data():
    """显示崇祯个人数据"""
    st.markdown('<div class="section-header">👤 崇祯个人数据</div>', unsafe_allow_html=True)

    # 获取玩家信息
    player_data = api_request(f"/api/user/{st.session_state.player_id}")

    if player_data:
        col1, col2 = st.columns([1, 1])

        with col1:
            st.markdown(f"""
            <div class="emperor-card">
                <h3>👑 皇帝信息</h3>
                <p><strong>年龄:</strong> {player_data.get('emperor_age', 17)}岁</p>
                <p><strong>健康:</strong> {player_data.get('emperor_health', 0.8):.1%}</p>
                <p><strong>洞察力:</strong> {player_data.get('emperor_insight', 0.6):.1%}</p>
                <p><strong>魅力:</strong> {player_data.get('emperor_charisma', 0.5):.1%}</p>
                <p><strong>智力:</strong> {player_data.get('emperor_intelligence', 0.7):.1%}</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown(f"""
            <div class="emperor-card">
                <h3>📊 游戏统计</h3>
                <p><strong>颁布诏书:</strong> {player_data.get('total_edicts_issued', 0)}道</p>
                <p><strong>召见大臣:</strong> {player_data.get('total_ministers_summoned', 0)}次</p>
                <p><strong>解决危机:</strong> {player_data.get('total_crises_resolved', 0)}次</p>
                <p><strong>游戏难度:</strong> {player_data.get('game_difficulty', 'normal')}</p>
            </div>
            """, unsafe_allow_html=True)

        # 属性图表
        st.markdown('<div class="section-header">📈 能力雷达图</div>', unsafe_allow_html=True)

        attributes = {
            '健康': player_data.get('emperor_health', 0.8),
            '洞察力': player_data.get('emperor_insight', 0.6),
            '魅力': player_data.get('emperor_charisma', 0.5),
            '智力': player_data.get('emperor_intelligence', 0.7)
        }

        fig = go.Figure()
        fig.add_trace(go.Scatterpolar(
            r=list(attributes.values()),
            theta=list(attributes.keys()),
            fill='toself',
            name='崇祯能力'
        ))

        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )),
            showlegend=True,
            title="崇祯皇帝能力雷达图"
        )

        st.plotly_chart(fig, use_container_width=True)

def show_empire_data():
    """显示大明势力数据"""
    st.markdown('<div class="section-header">🏛️ 大明势力数据</div>', unsafe_allow_html=True)

    # 获取资源状况
    resources = api_request(f"/api/game/resources/{st.session_state.player_id}")

    if resources:
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("国库银两", f"{resources.get('treasury', 0):,}两")

        with col2:
            st.metric("粮食储备", f"{resources.get('food', 0):,}石")

        with col3:
            st.metric("军队数量", f"{resources.get('military', 0):,}人")

        with col4:
            st.metric("人口总数", f"{resources.get('population', 0):,}人")

        # 资源趋势图
        st.markdown('<div class="section-header">📊 资源趋势</div>', unsafe_allow_html=True)

        # 模拟历史数据
        import numpy as np
        turns = list(range(1, 11))
        treasury_trend = [resources.get('treasury', 1000000) * (1 + np.random.uniform(-0.1, 0.1)) for _ in turns]
        food_trend = [resources.get('food', 500000) * (1 + np.random.uniform(-0.15, 0.1)) for _ in turns]

        fig = go.Figure()
        fig.add_trace(go.Scatter(x=turns, y=treasury_trend, mode='lines+markers', name='国库银两'))
        fig.add_trace(go.Scatter(x=turns, y=food_trend, mode='lines+markers', name='粮食储备'))

        fig.update_layout(
            title="资源变化趋势",
            xaxis_title="回合",
            yaxis_title="数量",
            hovermode='x unified'
        )

        st.plotly_chart(fig, use_container_width=True)

def show_map():
    """显示地图"""
    st.markdown('<div class="section-header">🗺️ 大明疆域</div>', unsafe_allow_html=True)

    # 获取地图信息
    map_data = api_request(f"/api/game/map/{st.session_state.player_id}")

    # 使用配置中的地区数据
    regions_data = DEFAULT_REGIONS

    # 创建地区卡片
    cols = st.columns(3)
    for i, region in enumerate(regions_data):
        with cols[i % 3]:
            stability_color = "🟢" if region["stability"] > 0.7 else "🟡" if region["stability"] > 0.4 else "🔴"
            prosperity_color = "🟢" if region["prosperity"] > 0.7 else "🟡" if region["prosperity"] > 0.4 else "🔴"
            loyalty_color = "🟢" if region["loyalty"] > 0.7 else "🟡" if region["loyalty"] > 0.4 else "🔴"

            st.markdown(f"""
            <div class="region-card">
                <h4>{region['name']} ({region['type']})</h4>
                <p><strong>人口:</strong> {region['population']:,}人</p>
                <p><strong>稳定:</strong> {stability_color} {region['stability']:.1%}</p>
                <p><strong>繁荣:</strong> {prosperity_color} {region['prosperity']:.1%}</p>
                <p><strong>忠诚:</strong> {loyalty_color} {region['loyalty']:.1%}</p>
            </div>
            """, unsafe_allow_html=True)

    # 地区统计图表
    st.markdown('<div class="section-header">📊 地区对比</div>', unsafe_allow_html=True)

    df = pd.DataFrame(regions_data)

    col1, col2 = st.columns(2)

    with col1:
        fig_pop = px.bar(df, x='name', y='population', title='各地区人口分布')
        fig_pop.update_layout(xaxis_title="地区", yaxis_title="人口")
        st.plotly_chart(fig_pop, use_container_width=True)

    with col2:
        fig_metrics = go.Figure()
        fig_metrics.add_trace(go.Bar(name='稳定', x=df['name'], y=df['stability']))
        fig_metrics.add_trace(go.Bar(name='繁荣', x=df['name'], y=df['prosperity']))
        fig_metrics.add_trace(go.Bar(name='忠诚', x=df['name'], y=df['loyalty']))

        fig_metrics.update_layout(
            title='各地区指标对比',
            xaxis_title="地区",
            yaxis_title="指标值",
            barmode='group'
        )
        st.plotly_chart(fig_metrics, use_container_width=True)

def show_ministers():
    """显示召见大臣页面"""
    st.markdown('<div class="section-header">👥 召见大臣</div>', unsafe_allow_html=True)

    # 获取大臣列表
    ministers_data = api_request(f"/api/game/ministers/{st.session_state.player_id}")

    # 使用配置中的大臣数据
    ministers = DEFAULT_MINISTERS

    col1, col2 = st.columns([1, 2])

    with col1:
        st.markdown("### 📋 可召见大臣")

        for minister in ministers:
            loyalty_color = "🟢" if minister["loyalty"] > 0.7 else "🟡" if minister["loyalty"] > 0.4 else "🔴"
            ability_color = "🟢" if minister["ability"] > 0.7 else "🟡" if minister["ability"] > 0.4 else "🔴"

            if st.button(f"{minister['name']} ({minister['position']})", key=f"minister_{minister['name']}", use_container_width=True):
                print(f'👥 选择召见大臣: {minister["name"]} ({minister["position"]})')
                st.session_state.selected_minister = minister
                st.session_state.chat_history = []

            st.markdown(f"""
            <div class="minister-card">
                <p><strong>派系:</strong> {minister['faction']}</p>
                <p><strong>忠诚:</strong> {loyalty_color} {minister['loyalty']:.1%}</p>
                <p><strong>能力:</strong> {ability_color} {minister['ability']:.1%}</p>
            </div>
            """, unsafe_allow_html=True)

    with col2:
        if st.session_state.selected_minister:
            show_minister_chat()
        else:
            st.info("请选择一位大臣进行召见")

def show_minister_chat():
    """显示与大臣的对话界面"""
    minister = st.session_state.selected_minister
    st.markdown(f"### 💬 与{minister['name']}对话")

    # 显示对话历史
    chat_container = st.container()
    with chat_container:
        for message in st.session_state.chat_history:
            if message["role"] == "user":
                st.markdown(f"""
                <div class="chat-message user-message">
                    <strong>👑 陛下:</strong> {message["content"]}
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div class="chat-message minister-message">
                    <strong>🎭 {minister['name']}:</strong> {message["content"]}
                </div>
                """, unsafe_allow_html=True)

    # 输入框
    with st.form("chat_form", clear_on_submit=True):
        user_input = st.text_area("请输入您要说的话:", placeholder=f"对{minister['name']}说些什么...")

        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            send_clicked = st.form_submit_button("💬 发送", use_container_width=True)

        with col2:
            end_clicked = st.form_submit_button("🚪 结束召见", use_container_width=True)

    if send_clicked and user_input:
        print(f'💬 用户对{minister["name"]}说: {user_input[:50]}...')

        # 添加用户消息
        st.session_state.chat_history.append({"role": "user", "content": user_input})

        # 生成大臣回复（使用LLM智能对话）
        minister_reply = generate_minister_reply(minister, user_input)
        print(f'🎭 {minister["name"]}回复: {minister_reply[:50]}...')
        st.session_state.chat_history.append({"role": "minister", "content": minister_reply})

        # 记录召见事件（可选，用于游戏统计）
        try:
            api_request("/api/game/minister/summon", "POST", {
                "player_id": st.session_state.player_id,
                "minister_name": minister["name"],
                "topic": user_input
            })
        except Exception as e:
            print(f'⚠️ 召见API调用失败（不影响对话）: {str(e)}')

        st.rerun()

    if end_clicked:
        print(f'🚪 结束与{minister["name"]}的召见')
        st.session_state.selected_minister = None
        st.session_state.chat_history = []
        st.rerun()

def generate_minister_reply(minister: Dict, user_input: str) -> str:
    """生成大臣回复（优先使用LLM智能对话）"""
    try:
        # 首先尝试调用LLM智能对话API
        print(f'🤖 尝试调用LLM对话API，大臣: {minister["name"]}')
        print(f'📝 当前player_id: {st.session_state.player_id}')
        print(f'💬 用户输入: {user_input}')

        # 根据大臣姓名查找对应的NPC ID
        npc_mapping = get_npc_id_mapping()
        npc_id = npc_mapping.get(minister["name"])

        print(f'🔍 NPC映射结果: {minister["name"]} -> {npc_id}')
        print(f'📋 完整映射: {npc_mapping}')

        if npc_id and st.session_state.player_id:
            print(f'✅ 准备调用NPC对话API')
            print(f'   - player_id: {st.session_state.player_id}')
            print(f'   - npc_id: {npc_id}')
            print(f'   - message: {user_input}')

            # 调用NPC对话API - 使用查询参数
            import urllib.parse
            encoded_message = urllib.parse.quote(user_input)
            chat_response = api_request(f"/api/npc/chat?player_id={st.session_state.player_id}&npc_id={npc_id}&message={encoded_message}", "POST")

            print(f'📥 API响应: {chat_response}')

            if chat_response and "npc_response" in chat_response:
                print(f'✅ LLM对话成功，大臣: {minister["name"]}')
                print(f'🎭 回复内容: {chat_response["npc_response"][:100]}...')
                return chat_response["npc_response"]
            else:
                print(f'⚠️ LLM对话API返回异常，大臣: {minister["name"]}')
                print(f'   响应内容: {chat_response}')
        else:
            if not npc_id:
                print(f'⚠️ 未找到对应NPC ID，大臣: {minister["name"]}')
            if not st.session_state.player_id:
                print(f'⚠️ player_id为空: {st.session_state.player_id}')

    except Exception as e:
        print(f'❌ LLM对话调用失败: {str(e)}')
        import traceback
        print(f'🔍 错误详情: {traceback.format_exc()}')

    # 如果LLM调用失败，回退到简单模拟
    print(f'🔄 回退到简单模拟回复，大臣: {minister["name"]}')
    return generate_fallback_reply(minister, user_input)

def get_npc_id_mapping() -> Dict[str, str]:
    """获取大臣姓名到角色ID的映射"""
    try:
        print('🔍 开始获取NPC ID映射...')

        # 从统一角色集合获取角色列表
        from pymongo import MongoClient
        client = MongoClient("mongodb://localhost:27017/")
        db = client["chongzhen_sim"]

        print('📊 连接MongoDB成功，查询unified_characters集合...')
        characters = list(db.unified_characters.find({}, {"_id": 1, "name": 1}))
        client.close()

        print(f'📋 从数据库获取到 {len(characters)} 个角色')

        # 构建映射字典
        mapping = {}
        for char in characters:
            char_name = char["name"]
            char_id = str(char["_id"])
            mapping[char_name] = char_id
            print(f'   - {char_name}: {char_id}')

        print(f'✅ 角色映射构建完成: {list(mapping.keys())}')
        return mapping

    except Exception as e:
        print(f'❌ 获取角色映射失败: {str(e)}')
        import traceback
        print(f'🔍 错误详情: {traceback.format_exc()}')
        return {}

def generate_fallback_reply(minister: Dict, user_input: str) -> str:
    """生成回退回复（简单模拟）"""
    replies = {
        "温体仁": [
            "臣以为此事需要慎重考虑，不可轻举妄动。",
            "陛下圣明，臣当遵旨而行。",
            "此事关系重大，臣建议先征询众臣意见。"
        ],
        "袁崇焕": [
            "臣愿为陛下分忧，定当竭尽全力！",
            "边关事务紧急，臣请陛下速做决断。",
            "臣在辽东多年，深知边防之重要。"
        ],
        "魏忠贤": [
            "奴婢定当为陛下办好此事。",
            "陛下所虑甚是，奴婢自有妙计。",
            "此事交给奴婢，陛下尽可放心。"
        ],
        "孙承宗": [
            "臣老矣，但仍愿为国效力。",
            "此事需要统筹规划，不可急于求成。",
            "臣在边关多年，深知其中艰难。"
        ]
    }

    import random
    return random.choice(replies.get(minister["name"], ["臣遵旨。"]))

def show_edict_editor():
    """显示诏书写作工具"""
    st.markdown('<div class="section-header">📜 诏书写作</div>', unsafe_allow_html=True)

    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("### ✍️ 诏书内容")

        # 诏书模板选择
        template_options = EDICT_TEMPLATES

        selected_template = st.selectbox("选择诏书模板:", list(template_options.keys()))

        if st.button("📋 使用模板", use_container_width=True):
            print(f'📋 使用诏书模板: {selected_template}')
            st.session_state.edict_draft = template_options[selected_template]
            st.rerun()

        # 诏书编辑器
        edict_content = st.text_area(
            "诏书内容:",
            value=st.session_state.edict_draft,
            height=300,
            placeholder="请输入诏书内容..."
        )

        # 更新草稿
        if edict_content != st.session_state.edict_draft:
            st.session_state.edict_draft = edict_content

        # 操作按钮
        col_btn1, col_btn2, col_btn3, col_btn4 = st.columns(4)

        with col_btn1:
            if st.button("🤖 AI润色", use_container_width=True):
                if edict_content:
                    print(f'🤖 AI润色诏书，原文长度: {len(edict_content)}字')
                    polished_content = polish_edict_with_ai(edict_content)
                    st.session_state.edict_draft = polished_content
                    print(f'✨ 润色完成，新文长度: {len(polished_content)}字')
                    st.rerun()
                else:
                    print('⚠️ 尝试润色空诏书')
                    st.warning("请先输入诏书内容")

        with col_btn2:
            if st.button("💾 保存草稿", use_container_width=True):
                print(f'💾 保存诏书草稿，长度: {len(edict_content)}字')
                st.success("草稿已保存")

        with col_btn3:
            if st.button("🗑️ 清空", use_container_width=True):
                print('🗑️ 清空诏书草稿')
                st.session_state.edict_draft = ""
                st.rerun()

        with col_btn4:
            if st.button("📤 提交诏书", use_container_width=True):
                if edict_content:
                    print(f'📤 提交诏书，长度: {len(edict_content)}字')
                    submit_edict(edict_content)
                else:
                    print('⚠️ 尝试提交空诏书')
                    st.warning("请先输入诏书内容")

    with col2:
        st.markdown("### 🎯 AI建议")

        # AI建议类型
        suggestion_type = st.selectbox("建议类型:", [
            "政治建议",
            "经济建议",
            "军事建议",
            "民生建议",
            "外交建议"
        ])

        if st.button("💡 获取AI建议", use_container_width=True):
            print(f'💡 获取AI建议，类型: {suggestion_type}')
            suggestion = get_ai_suggestion(suggestion_type)
            print(f'🎯 AI建议: {suggestion[:50]}...')
            st.markdown(f"""
            <div style="background-color: #f0f8ff; padding: 1rem; border-radius: 8px; border-left: 4px solid #4682b4;">
                <strong>AI建议:</strong><br>
                {suggestion}
            </div>
            """, unsafe_allow_html=True)

            if st.button("📝 采用建议", key="adopt_suggestion"):
                print(f'📝 采用AI建议并添加到诏书')
                st.session_state.edict_draft += f"\n\n{suggestion}"
                st.rerun()

        # 诏书历史
        st.markdown("### 📚 历史诏书")

        history_edicts = [
            {"title": "赈灾诏书", "date": "崇祯1年3月", "status": "已执行"},
            {"title": "军备诏书", "date": "崇祯1年2月", "status": "已执行"},
            {"title": "税收诏书", "date": "崇祯1年1月", "status": "已执行"}
        ]

        for edict in history_edicts:
            status_color = "🟢" if edict["status"] == "已执行" else "🟡"
            st.markdown(f"""
            <div style="background-color: #f9f9f9; padding: 0.5rem; margin: 0.5rem 0; border-radius: 4px;">
                <strong>{edict['title']}</strong><br>
                <small>{edict['date']} {status_color} {edict['status']}</small>
            </div>
            """, unsafe_allow_html=True)

def polish_edict_with_ai(content: str) -> str:
    """使用AI润色诏书"""
    # 这里可以调用真实的AI API，现在先模拟
    polished_suggestions = [
        f"朕深思熟虑，{content}",
        f"天下苍生为重，{content}",
        f"为国为民，{content}",
        f"朕心忧民，{content}"
    ]

    import random
    return random.choice(polished_suggestions)

def get_ai_suggestion(suggestion_type: str) -> str:
    """获取AI建议"""
    return random.choice(AI_SUGGESTIONS.get(suggestion_type, ["暂无建议"]))

def submit_edict(content: str):
    """提交诏书并开始回合推进"""
    # 使用查询参数而不是JSON请求体
    import urllib.parse
    encoded_content = urllib.parse.quote(content)
    result = api_request(f"/api/game/edict/submit?player_id={st.session_state.player_id}&edict_content={encoded_content}", "POST")

    if result:
        st.success("诏书提交成功！回合推进已开始...")

        # 清空草稿
        st.session_state.edict_draft = ""

        # 显示回合进度
        show_turn_progress()
    else:
        st.error("诏书提交失败，请重试")

def show_turn_progress():
    """显示回合推进进度"""
    st.markdown("### ⏳ 回合推进中...")

    # 创建进度显示容器
    progress_container = st.container()

    with progress_container:
        # 获取进度
        progress_data = api_request(f"/api/game/turn/progress/{st.session_state.player_id}")

        if progress_data and progress_data.get("progress"):
            progress = progress_data["progress"]

            # 显示当前阶段
            phase_names = {
                "minister_action": "🏛️ 大臣行动阶段",
                "environment_action": "🌍 环境演化阶段",
                "emperor_action": "👑 阶段"
            }

            current_phase = progress.get("current_phase", "unknown")
            phase_name = phase_names.get(current_phase, "未知阶段")

            st.info(f"当前阶段: {phase_name}")

            # 显示进度条
            phase_progress = progress.get("phase_progress", 0.0)
            st.progress(phase_progress)

            # 显示详细状态
            detailed_status = progress.get("detailed_status", "处理中...")
            st.text(detailed_status)

            # 显示当前Agent
            current_agent = progress.get("current_agent", "")
            if current_agent:
                agent_names = {
                    "minister_action_agent": "大臣行动Agent",
                    "environment_evolution_agent": "环境演化Agent",
                    "notification_agent": "通知生成Agent"
                }
                agent_name = agent_names.get(current_agent, current_agent)
                st.text(f"正在执行: {agent_name}")

            # 检查是否完成
            if progress_data.get("is_completed"):
                st.success("✅ 回合推进完成！")
                st.balloons()
                # 刷新页面数据
                time.sleep(2)
                st.rerun()
            else:
                # 自动刷新
                time.sleep(1)
                st.rerun()
        else:
            st.info("正在初始化回合推进...")
            time.sleep(1)
            st.rerun()

def main():
    """主函数"""
    print('🚀 崇祯模拟器前端启动')
    print(f'🔗 API基础URL: {API_BASE_URL}')

    init_session_state()

    if st.session_state.player_id is None:
        print('🔐 显示登录页面')
        login_page()
    else:
        print(f'🎮 显示游戏界面，玩家ID: {st.session_state.player_id}')
        main_interface()

if __name__ == "__main__":
    print('=' * 50)
    print('👑 崇祯模拟器 Web UI')
    print('=' * 50)
    main()
