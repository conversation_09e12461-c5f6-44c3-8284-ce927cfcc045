# 崇祯模拟器后端

基于LLM的历史模拟游戏后端系统，让玩家扮演明朝崇祯皇帝，通过发布诏书来治理帝国。

后端启动：  
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

前端启动：  
python -m streamlit run ui/main.py --server.port 8501 --server.address 0.0.0.0

## 项目特色

- **奏折驱动**：通过月度奏折了解帝国状况
- **诏书系统**：发布复杂的治国指令
- **动态推演**：AI驱动的帝国状态变化
- **角色扮演**：与历史人物深度对话
- **危机管理**：应对各种历史危机事件

## 技术架构

- **Web框架**：FastAPI
- **数据库**：MongoDB + Motor (异步驱动)
- **AI模型**：OpenAI GPT-4 / 本地模型
- **Agent系统**：多Agent协作推演
- **工具系统**：模块化的游戏逻辑工具

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd chongzhen_sim_backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境

复制 `.env` 文件并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，设置必要的配置项：

- `OPENAI_API_KEY`：OpenAI API密钥
- `DATABASE_URL`：数据库连接字符串
- 其他配置项根据需要调整

### 3. 初始化数据库

```bash
# 运行数据库迁移
python -c "from app.db.database import init_db; import asyncio; asyncio.run(init_db())"
```

### 4. 启动服务

```bash
# 开发模式
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或使用启动脚本
chmod +x run_server.sh
./run_server.sh
```

### 5. 访问API

- API文档：http://localhost:8000/docs
- 健康检查：http://localhost:8000/health

## API接口

### 用户管理
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/user/{player_id}` - 获取用户信息

### 游戏核心
- `POST /api/game/edict/submit` - 提交诏书
- `POST /api/game/edict/confirm` - 确认执行诏书
- `GET /api/game/status/{player_id}` - 获取游戏状态
- `POST /api/game/summon` - 召见大臣

### 管理功能
- `POST /api/admin/force_simulate` - 强制推演
- `POST /api/admin/reset_game` - 重置游戏

## 项目结构

```
chongzhen_sim_backend/
├── app/
│   ├── api/                    # API路由
│   ├── agents/                 # AI Agent系统
│   ├── tools/                  # 游戏工具
│   ├── services/               # 业务逻辑
│   ├── db/                     # 数据库模型
│   ├── schemas/                # 数据结构
│   ├── utils/                  # 工具函数
│   └── main.py                 # 应用入口
├── requirements.txt            # 依赖列表
├── .env                        # 环境配置
└── README.md                   # 项目说明
```

## 核心概念

### Agent系统
- **EdictRefineAgent**：诏书润色
- **SimulateAgent**：主要推演
- **StoryProgressAgent**：世界自演
- **DataSummaryAgent**：数值总结
- **OutputGenerationAgent**：输出生成
- **RoleplayAgent**：角色扮演

### 工具系统
- **CrisisGenerator**：危机生成
- **EdictFormatter**：指令格式化
- **ResourceCalculator**：资源计算
- **MapQuery**：地图查询
- **TechSimulator**：技术模拟

### 数据模型
- **Player**：玩家信息和崇祯属性
- **UnifiedCharacter**：统一角色模型（包含游戏属性和对话属性）
- **MapRegion**：地区信息
- **Faction**：其他势力
- **Conversation**：对话记录

## 开发指南

### 添加新Agent

1. 在 `app/agents/` 创建新的Agent类
2. 继承 `BaseAgent` 并实现必要方法
3. 在 `AgentOrchestrator` 中注册

### 添加新工具

1. 在 `app/tools/` 创建工具模块
2. 使用 `@tool` 装饰器注册工具
3. 工具会自动被Agent发现和使用

### 数据库管理

```bash
# 数据库会在首次启动时自动初始化
# 如需重置数据库，删除MongoDB中的集合即可

# 数据迁移工具
python migrate_characters.py  # 合并角色数据
python migrate_characters.py --rollback  # 回滚迁移
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t chongzhen-sim-backend .

# 运行容器
docker run -p 8000:8000 -e OPENAI_API_KEY=your_key chongzhen-sim-backend
```

### 生产环境

```bash
# 使用Gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。
