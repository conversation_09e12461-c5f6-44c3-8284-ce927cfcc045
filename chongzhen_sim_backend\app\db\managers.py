"""
数据库管理类 - 类似ChatLogManager的设计模式
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from bson import ObjectId
from app.db.database import get_db_manager
from app.db.models.player import Player
from app.db.models.conversation import Conversation, UnifiedCharacter, ConversationMessage
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class PlayerManager:
    """玩家数据管理器"""
    
    def __init__(self):
        self.db_manager = None
        self.collection = None
    
    async def initialize(self):
        """初始化管理器"""
        self.db_manager = get_db_manager()
        self.collection = self.db_manager.players
    
    async def create_player(self, username: str, password: str, email: Optional[str] = None) -> Player:
        """创建新玩家"""
        try:
            # 检查用户名是否已存在
            existing = await self.collection.find_one({"username": username})
            if existing:
                raise ValueError("用户名已存在")
            
            # 创建玩家对象
            player = Player(
                username=username,
                email=email,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # 设置密码
            player.set_password(password)
            
            # 插入数据库
            player_dict = player.model_dump(by_alias=True, exclude={"id"})

            # 如果email为None，则从字典中删除该字段，避免索引冲突
            if player_dict.get("email") is None:
                player_dict.pop("email", None)

            result = await self.collection.insert_one(player_dict)
            player.id = str(result.inserted_id)
            
            logger.info(f"创建新玩家: {username}")
            return player
            
        except Exception as e:
            logger.error(f"创建玩家失败: {e}")
            raise
    
    async def get_player_by_username(self, username: str) -> Optional[Player]:
        """根据用户名获取玩家"""
        try:
            player_data = await self.collection.find_one({"username": username})
            print('player_data:', player_data)
            if player_data:
                return Player.from_dict(player_data)
            return None
        except Exception as e:
            logger.error(f"获取玩家失败: {e}")
            return None
    
    async def get_player_by_id(self, player_id: str) -> Optional[Player]:
        """根据ID获取玩家"""
        try:
            player_data = await self.collection.find_one({"_id": ObjectId(player_id)})
            if player_data:
                return Player.from_dict(player_data)
            return None
        except Exception as e:
            logger.error(f"获取玩家失败: {e}")
            return None
    
    async def update_player(self, player: Player) -> bool:
        """更新玩家数据"""
        try:
            player.updated_at = datetime.utcnow()
            player_dict = player.model_dump(by_alias=True, exclude={"id"})
            
            result = await self.collection.update_one(
                {"_id": ObjectId(player.id)},
                {"$set": player_dict}
            )
            
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"更新玩家失败: {e}")
            return False
    
    async def delete_player(self, player_id: str) -> bool:
        """删除玩家"""
        try:
            result = await self.collection.delete_one({"_id": ObjectId(player_id)})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除玩家失败: {e}")
            return False
    
    async def authenticate_player(self, username: str, password: str) -> Optional[Player]:
        """验证玩家登录"""
        try:
            player = await self.get_player_by_username(username)
            if player and player.verify_password(password):
                # 更新最后活跃时间
                player.last_active = datetime.utcnow()
                await self.update_player(player)
                return player
            return None
        except Exception as e:
            logger.error(f"玩家认证失败: {e}")
            return None

class RegionManager:
    """地区数据管理器"""
    
    def __init__(self):
        self.db_manager = None
        self.collection = None
    
    async def initialize(self):
        """初始化管理器"""
        self.db_manager = get_db_manager()
        self.collection = self.db_manager.map_regions
    
    async def get_all_regions(self) -> List[Dict[str, Any]]:
        """获取所有地区"""
        try:
            cursor = self.collection.find({})
            regions = await cursor.to_list(length=None)
            return regions
        except Exception as e:
            logger.error(f"获取地区数据失败: {e}")
            return []
    
    async def get_region_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取地区"""
        try:
            region = await self.collection.find_one({"name": name})
            return region
        except Exception as e:
            logger.error(f"获取地区失败: {e}")
            return None
    
    async def update_region(self, name: str, updates: Dict[str, Any]) -> bool:
        """更新地区数据"""
        try:
            result = await self.collection.update_one(
                {"name": name},
                {"$set": updates}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"更新地区失败: {e}")
            return False

class CharacterManager:
    """角色数据管理器"""
    
    def __init__(self):
        self.db_manager = None
        self.collection = None
    
    async def initialize(self):
        """初始化管理器"""
        self.db_manager = get_db_manager()
        self.collection = self.db_manager.characters
    
    async def get_all_characters(self) -> List[Dict[str, Any]]:
        """获取所有角色"""
        try:
            cursor = self.collection.find({})
            characters = await cursor.to_list(length=None)
            return characters
        except Exception as e:
            logger.error(f"获取角色数据失败: {e}")
            return []
    
    async def get_character_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取角色"""
        try:
            character = await self.collection.find_one({"name": name})
            return character
        except Exception as e:
            logger.error(f"获取角色失败: {e}")
            return None
    
    async def update_character(self, name: str, updates: Dict[str, Any]) -> bool:
        """更新角色数据"""
        try:
            result = await self.collection.update_one(
                {"name": name},
                {"$set": updates}
            )
            return result.modified_count > 0
        except Exception as e:
            logger.error(f"更新角色失败: {e}")
            return False

# TODO cyx 这个要再考虑下有没有
class EdictManager:
    """诏书管理器"""
    
    def __init__(self):
        self.db_manager = None
        self.collection = None
    
    async def initialize(self):
        """初始化管理器"""
        self.db_manager = get_db_manager()
        self.collection = self.db_manager.edicts
    
    async def create_edict(self, player_id: str, title: str, content: str, edict_type: str = "general") -> str:
        """创建诏书"""
        try:
            edict_data = {
                "player_id": player_id,
                "title": title,
                "content": content,
                "edict_type": edict_type,
                "created_at": datetime.utcnow(),
                "status": "active"
            }
            
            result = await self.collection.insert_one(edict_data)
            logger.info(f"创建诏书: {title}")
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"创建诏书失败: {e}")
            raise
    
    async def get_player_edicts(self, player_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取玩家的诏书"""
        try:
            cursor = self.collection.find({"player_id": player_id}).sort("created_at", -1).limit(limit)
            edicts = await cursor.to_list(length=None)
            return edicts
        except Exception as e:
            logger.error(f"获取诏书失败: {e}")
            return []

class ConversationManager:
    """对话管理器"""

    def __init__(self):
        self.collection = None
        self.npc_collection = None

    async def initialize(self):
        """初始化管理器"""
        db_manager = get_db_manager()
        self.collection = db_manager.conversations
        self.npc_collection = db_manager.npc_profiles
        logger.info("对话管理器初始化完成")

    async def create_conversation(self, player_id: str, npc_id: str, npc_name: str, session_id: str) -> Conversation:
        """创建新对话"""
        try:
            conversation = Conversation(
                player_id=player_id,
                npc_id=npc_id,
                npc_name=npc_name,
                session_id=session_id
            )

            conversation_dict = conversation.to_dict()
            result = await self.collection.insert_one(conversation_dict)
            conversation.id = str(result.inserted_id)

            logger.info(f"创建新对话: {player_id} -> {npc_name}")
            return conversation

        except Exception as e:
            logger.error(f"创建对话失败: {e}")
            raise

    async def get_conversation(self, player_id: str, npc_id: str, session_id: str) -> Optional[Conversation]:
        """获取对话记录"""
        try:
            conversation_data = await self.collection.find_one({
                "player_id": player_id,
                "npc_id": npc_id,
                "session_id": session_id
            })

            if conversation_data:
                return Conversation.from_dict(conversation_data)
            return None

        except Exception as e:
            logger.error(f"获取对话失败: {e}")
            return None

    async def add_message(self, conversation_id: str, role: str, content: str) -> bool:
        """添加消息到对话"""
        try:
            message = ConversationMessage(role=role, content=content)

            result = await self.collection.update_one(
                {"_id": ObjectId(conversation_id)},
                {
                    "$push": {"messages": message.model_dump()},
                    "$inc": {"message_count": 1},
                    "$set": {"updated_at": datetime.utcnow()}
                }
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"添加消息失败: {e}")
            return False

    async def get_npc_profile(self, npc_id: str) -> Optional[UnifiedCharacter]:
        """获取角色档案"""
        try:
            npc_data = await self.npc_collection.find_one({"_id": ObjectId(npc_id)})
            if npc_data:
                return UnifiedCharacter.from_dict(npc_data)
            return None

        except Exception as e:
            logger.error(f"获取角色档案失败: {e}")
            return None

    async def create_npc_profile(self, npc_data: dict) -> UnifiedCharacter:
        """创建角色档案"""
        try:
            npc = UnifiedCharacter(**npc_data)
            npc_dict = npc.to_dict()
            result = await self.npc_collection.insert_one(npc_dict)
            npc.id = str(result.inserted_id)

            logger.info(f"创建角色档案: {npc.name}")
            return npc

        except Exception as e:
            logger.error(f"创建角色档案失败: {e}")
            raise


# 全局管理器实例
player_manager = PlayerManager()
region_manager = RegionManager()
character_manager = CharacterManager()
edict_manager = EdictManager()
conversation_manager = ConversationManager()

async def initialize_all_managers():
    """初始化所有管理器"""
    await player_manager.initialize()
    await region_manager.initialize()
    await character_manager.initialize()
    await edict_manager.initialize()
    await conversation_manager.initialize()
    logger.info("所有数据库管理器初始化完成")
