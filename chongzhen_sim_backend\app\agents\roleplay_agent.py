"""
角色扮演Agent - 负责扮演历史人物与玩家对话
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from app.services.llm_service import llm_service
from app.db.managers import conversation_manager
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class RoleplayInput(AgentInput):
    """角色扮演输入"""
    character_name: str
    player_question: str
    current_situation: Dict[str, Any]
    conversation_history: List[Dict[str, str]] = []

class RoleplayOutput(BaseModel):
    """角色扮演输出"""
    character_response: str
    character_mood: str
    loyalty_change: float = 0.0
    hidden_thoughts: str = ""

class RoleplayAgent(BaseAgent):
    """角色扮演Agent"""
    
    # 函数前面有下划线 _ 的是 私有方法，没有下划线的是 公有方法。
    
    def get_system_prompt(self) -> str:
        return """你是明朝历史人物扮演系统，能够准确扮演各种历史人物与皇帝对话。

你的职责：
1. 根据历史人物的性格、立场、背景进行角色扮演
2. 考虑当前政治局势对人物态度的影响
3. 体现人物的政治立场、个人利益和忠诚度
4. 生成符合历史背景的对话内容
5. 评估对话对人物忠诚度的影响

角色扮演要求：
- 语言风格符合明朝官场用语
- 体现人物的性格特点和政治立场
- 考虑人物的个人利益和派系关系
- 适当表现奉承、欺骗、忠诚等复杂情感
- 根据皇帝的话语调整态度和忠诚度

常见人物类型：
- 内阁大学士：温体仁、周延儒等
- 兵部尚书：袁崇焕、孙承宗等  
- 户部尚书：负责财政
- 地方总督：各省总督巡抚
- 宦官：魏忠贤等
- 将领：各地军事将领"""

    async def process(self, input_data: RoleplayInput) -> AgentOutput:
        """处理角色扮演"""
        try:
            if not await self.validate_input(input_data):
                return self.create_error_output("输入数据无效")
            
            # 获取角色信息
            character_info = await self._get_character_info(input_data.character_name)
            
            # 构建角色扮演消息
            messages = [
                {"role": "system", "content": self._build_character_prompt(character_info)},
                {"role": "user", "content": self._build_roleplay_prompt(input_data)}
            ]
            
            # 调用LLM进行角色扮演
            response = await llm_service.chat_completion(
                messages=[messages[1]],  # 只传用户消息
                system_prompt=messages[0]["content"],  # 系统提示词
                temperature=0.9,
                max_tokens=1500
            )
            
            # 解析角色响应
            roleplay_result = self._parse_roleplay_response(response, input_data)
            
            logger.info(f"角色扮演完成，玩家ID: {input_data.player_id}, 角色: {input_data.character_name}")
            return self.create_success_output(roleplay_result)
            
        except Exception as e:
            logger.error(f"角色扮演失败: {e}")
            return self.create_error_output(f"角色扮演失败: {str(e)}")
    
    async def _get_character_info(self, character_name: str) -> Dict[str, Any]:
        """获取角色信息"""
        # 这里应该从数据库查询角色信息
        # 暂时返回默认信息
        character_profiles = {
            "温体仁": {
                "position": "内阁首辅",
                "personality": "谨慎保守，善于察言观色",
                "loyalty": 0.7,
                "political_stance": "维护传统，反对改革",
                "personal_interests": "保持权位，避免风险"
            },
            "袁崇焕": {
                "position": "兵部尚书",
                "personality": "刚直不阿，军事才能出众",
                "loyalty": 0.9,
                "political_stance": "主战派，支持军事改革",
                "personal_interests": "保卫边疆，建功立业"
            },
            "魏忠贤": {
                "position": "司礼监太监",
                "personality": "狡诈多疑，权欲极强",
                "loyalty": 0.3,
                "political_stance": "维护宦官集团利益",
                "personal_interests": "扩大权力，排除异己"
            }
        }
        
        return character_profiles.get(character_name, {
            "position": "朝廷官员",
            "personality": "谨慎务实",
            "loyalty": 0.6,
            "political_stance": "中立",
            "personal_interests": "保持现状"
        })
    
    def _build_character_prompt(self, character_info: Dict[str, Any]) -> str:
        """构建角色提示"""
        return f"""你现在要扮演明朝官员，具有以下特征：

职位：{character_info.get('position', '朝廷官员')}
性格：{character_info.get('personality', '谨慎务实')}
忠诚度：{character_info.get('loyalty', 0.6)}
政治立场：{character_info.get('political_stance', '中立')}
个人利益：{character_info.get('personal_interests', '保持现状')}

扮演要求：
1. 严格按照角色性格和立场回答
2. 使用明朝官场用语和礼仪
3. 考虑个人利益和政治立场
4. 适当表现对皇帝的态度
5. 可以有所保留或委婉表达

请完全进入角色，以第一人称回答皇帝的问题。"""
    
    def _build_roleplay_prompt(self, input_data: RoleplayInput) -> str:
        """构建角色扮演提示"""
        situation_str = self.format_context(input_data.current_situation)
        history_str = self._format_conversation_history(input_data.conversation_history)
        
        return f"""
当前朝廷情况：
{situation_str}

对话历史：
{history_str}

皇帝问道："{input_data.player_question}"

请以角色身份回答皇帝的问题，并在回答后说明：
【心情】：（角色当前的心情状态）
【内心想法】：（角色的真实想法，皇帝看不到）
"""
    
    def _format_conversation_history(self, history: List[Dict[str, str]]) -> str:
        """格式化对话历史"""
        if not history:
            return "（首次召见）"
        
        formatted_history = []
        for exchange in history[-3:]:  # 只显示最近3轮对话
            if exchange.get('speaker') == 'emperor':
                formatted_history.append(f"皇帝：{exchange.get('content', '')}")
            else:
                formatted_history.append(f"臣：{exchange.get('content', '')}")
        
        return "\n".join(formatted_history)
    
    def _parse_roleplay_response(
        self, 
        response: str, 
        input_data: RoleplayInput
    ) -> RoleplayOutput:
        """解析角色扮演响应"""
        # 分离主要回答和元信息
        character_response = response
        character_mood = "恭敬"
        hidden_thoughts = ""
        loyalty_change = 0.0
        
        # 提取心情和内心想法
        lines = response.split('\n')
        main_response_lines = []
        
        for line in lines:
            line = line.strip()
            if '【心情】' in line:
                character_mood = line.replace('【心情】', '').replace('：', '').strip()
            elif '【内心想法】' in line:
                hidden_thoughts = line.replace('【内心想法】', '').replace('：', '').strip()
            elif not line.startswith('【'):
                main_response_lines.append(line)
        
        character_response = '\n'.join(main_response_lines).strip()
        
        # 简单的忠诚度变化计算
        if "赞同" in input_data.player_question or "支持" in input_data.player_question:
            loyalty_change = 0.1
        elif "质疑" in input_data.player_question or "责备" in input_data.player_question:
            loyalty_change = -0.1
        
        return RoleplayOutput(
            character_response=character_response,
            character_mood=character_mood,
            loyalty_change=loyalty_change,
            hidden_thoughts=hidden_thoughts
        )
