#!/usr/bin/env python3
"""
数据库迁移脚本：合并 characters 和 npc_profiles 集合
"""

import asyncio
from pymongo import MongoClient
from datetime import datetime
from typing import Dict, List, Any
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config import settings
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

def get_mongo_client():
    """获取MongoDB客户端"""
    return MongoClient(settings.MONGODB_URL)

def migrate_data():
    """执行数据迁移"""
    try:
        client = get_mongo_client()
        db = client[settings.DATABASE_NAME]
        
        # 获取现有数据
        characters_data = list(db.characters.find({}))
        npc_profiles_data = list(db.npc_profiles.find({}))
        
        logger.info(f"找到 {len(characters_data)} 个角色记录")
        logger.info(f"找到 {len(npc_profiles_data)} 个NPC档案记录")
        
        # 创建姓名到数据的映射
        characters_by_name = {char["name"]: char for char in characters_data}
        npc_profiles_by_name = {npc["name"]: npc for npc in npc_profiles_data}
        
        # 获取所有唯一的角色名称
        all_names = set(characters_by_name.keys()) | set(npc_profiles_by_name.keys())
        logger.info(f"总共需要合并 {len(all_names)} 个角色")
        
        # 创建新的统一角色集合
        unified_characters = []
        
        for name in all_names:
            char_data = characters_by_name.get(name, {})
            npc_data = npc_profiles_by_name.get(name, {})
            
            # 构建统一的角色数据
            unified_char = create_unified_character(name, char_data, npc_data)
            unified_characters.append(unified_char)
            
            logger.info(f"合并角色: {name}")
        
        # 备份原有数据
        backup_collections(db)
        
        # 创建新的统一集合
        if unified_characters:
            # 删除旧的统一角色集合（如果存在）
            if "unified_characters" in db.list_collection_names():
                db.drop_collection("unified_characters")
            
            # 插入新数据
            result = db.unified_characters.insert_many(unified_characters)
            logger.info(f"成功插入 {len(result.inserted_ids)} 个统一角色记录")
            
            # 创建索引
            create_indexes(db)
            
            logger.info("数据迁移完成！")
            
            # 显示迁移结果
            show_migration_result(db)
        else:
            logger.warning("没有找到需要迁移的数据")
        
        client.close()
        
    except Exception as e:
        logger.error(f"数据迁移失败: {e}")
        raise

def create_unified_character(name: str, char_data: Dict, npc_data: Dict) -> Dict[str, Any]:
    """创建统一的角色数据"""
    now = datetime.utcnow()
    
    # 基本信息（优先使用npc_data，因为它更完整）
    title = npc_data.get("title", char_data.get("position", ""))
    age = char_data.get("age", 40)
    health = char_data.get("health", 0.8)
    
    # 游戏机制属性
    game_stats = {
        "loyalty": char_data.get("loyalty", 0.6),
        "administrative_ability": char_data.get("administrative_ability", 0.5),
        "military_ability": char_data.get("military_ability", 0.5),
        "diplomatic_ability": char_data.get("diplomatic_ability", 0.5),
        "economic_ability": char_data.get("economic_ability", 0.5),
        "faction": char_data.get("faction", ""),
        "political_stance": char_data.get("political_stance", ""),
        "personal_interests": char_data.get("personal_interests", ""),
        "allies": parse_list_field(char_data.get("allies", "")),
        "enemies": parse_list_field(char_data.get("enemies", "")),
        "personal_wealth": char_data.get("personal_wealth", 10000.0),
        "land_holdings": char_data.get("land_holdings", 0),
        "private_army": char_data.get("private_army", 0),
        "major_achievements": char_data.get("major_achievements", ""),
        "major_failures": char_data.get("major_failures", ""),
        "current_location": char_data.get("current_location", "京师")
    }
    
    # 对话系统属性
    dialogue_profile = {
        "personality": npc_data.get("personality", char_data.get("personality", "")),
        "background": npc_data.get("background", ""),
        "speaking_style": npc_data.get("speaking_style", ""),
        "system_prompt": npc_data.get("system_prompt", ""),
        "relationship_with_emperor": npc_data.get("relationship_with_emperor", "")
    }
    
    # 互动历史
    interaction_history = []
    if char_data.get("interaction_history"):
        try:
            import json
            interaction_history = json.loads(char_data["interaction_history"])
        except:
            interaction_history = []
    
    # 状态信息
    is_active = npc_data.get("is_active", char_data.get("is_active", True))
    
    # 时间信息
    created_at = npc_data.get("created_at") or char_data.get("created_at") or now
    updated_at = npc_data.get("updated_at") or char_data.get("updated_at") or now
    
    return {
        "name": name,
        "title": title,
        "age": age,
        "health": health,
        "game_stats": game_stats,
        "dialogue_profile": dialogue_profile,
        "interaction_history": interaction_history,
        "is_active": is_active,
        "created_at": created_at,
        "updated_at": updated_at
    }

def parse_list_field(field_value: str) -> List[str]:
    """解析逗号分隔的字符串字段为列表"""
    if not field_value:
        return []
    return [item.strip() for item in field_value.split(',') if item.strip()]

def backup_collections(db):
    """备份原有集合"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 备份characters集合
        if "characters" in db.list_collection_names():
            characters_backup = f"characters_backup_{timestamp}"
            db.characters.aggregate([{"$out": characters_backup}])
            logger.info(f"characters集合已备份为: {characters_backup}")
        
        # 备份npc_profiles集合
        if "npc_profiles" in db.list_collection_names():
            npc_backup = f"npc_profiles_backup_{timestamp}"
            db.npc_profiles.aggregate([{"$out": npc_backup}])
            logger.info(f"npc_profiles集合已备份为: {npc_backup}")
            
    except Exception as e:
        logger.error(f"备份失败: {e}")
        raise

def create_indexes(db):
    """为新集合创建索引"""
    try:
        collection = db.unified_characters
        
        # 创建索引
        collection.create_index("name", unique=True)
        collection.create_index("title")
        collection.create_index("is_active")
        collection.create_index("game_stats.faction")
        collection.create_index("game_stats.loyalty")
        collection.create_index("created_at")
        
        logger.info("索引创建完成")
        
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
        raise

def show_migration_result(db):
    """显示迁移结果"""
    try:
        unified_count = db.unified_characters.count_documents({})
        logger.info(f"统一角色集合中共有 {unified_count} 个角色")
        
        # 显示一些示例数据
        sample_chars = list(db.unified_characters.find({}).limit(3))
        for char in sample_chars:
            logger.info(f"示例角色: {char['name']} - {char['title']}")
            
    except Exception as e:
        logger.error(f"显示结果失败: {e}")

def rollback_migration():
    """回滚迁移（恢复备份）"""
    try:
        client = get_mongo_client()
        db = client[settings.DATABASE_NAME]
        
        # 查找最新的备份
        collections = db.list_collection_names()
        char_backups = [c for c in collections if c.startswith("characters_backup_")]
        npc_backups = [c for c in collections if c.startswith("npc_profiles_backup_")]
        
        if char_backups:
            latest_char_backup = sorted(char_backups)[-1]
            db.drop_collection("characters")
            db[latest_char_backup].aggregate([{"$out": "characters"}])
            logger.info(f"已从 {latest_char_backup} 恢复 characters 集合")
        
        if npc_backups:
            latest_npc_backup = sorted(npc_backups)[-1]
            db.drop_collection("npc_profiles")
            db[latest_npc_backup].aggregate([{"$out": "npc_profiles"}])
            logger.info(f"已从 {latest_npc_backup} 恢复 npc_profiles 集合")
        
        # 删除统一集合
        if "unified_characters" in collections:
            db.drop_collection("unified_characters")
            logger.info("已删除 unified_characters 集合")
        
        client.close()
        logger.info("迁移回滚完成")
        
    except Exception as e:
        logger.error(f"回滚失败: {e}")
        raise

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="角色数据迁移工具")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")
    parser.add_argument("--dry-run", action="store_true", help="试运行（不实际修改数据）")
    
    args = parser.parse_args()
    
    if args.rollback:
        logger.info("开始回滚迁移...")
        rollback_migration()
    else:
        logger.info("开始数据迁移...")
        if args.dry_run:
            logger.info("这是试运行，不会实际修改数据")
            # TODO: 实现试运行逻辑
        else:
            migrate_data()

if __name__ == "__main__":
    main()
