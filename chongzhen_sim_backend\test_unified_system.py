#!/usr/bin/env python3
"""
测试合并后的统一角色系统
"""

import requests
import time
from pymongo import MongoClient

# 服务器配置
API_BASE_URL = "http://127.0.0.1:8000"

def test_unified_character_system():
    """测试统一角色系统"""
    print("🧪 测试合并后的统一角色系统")
    print("=" * 60)
    
    # 1. 检查数据库中的统一角色
    print("\n1️⃣ 检查数据库中的统一角色...")
    try:
        client = MongoClient("mongodb://localhost:27017/")
        db = client["chongzhen_sim"]
        characters = list(db.unified_characters.find({}, {"_id": 1, "name": 1, "title": 1}))
        client.close()
        
        if characters:
            print("✅ 统一角色数据正常:")
            for char in characters:
                print(f"   - {char['name']} ({char['title']}): {char['_id']}")
        else:
            print("❌ 未找到统一角色数据")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    # 2. 注册测试用户
    print("\n2️⃣ 注册测试用户...")
    try:
        username = f"unified_test_{int(time.time())}"
        response = requests.post(f"{API_BASE_URL}/api/user/register", json={
            "username": username,
            "password": "test123",
            "game_difficulty": "normal"
        })
        
        if response.status_code == 200:
            player_data = response.json()
            player_id = player_data.get("id")
            print(f"✅ 用户注册成功: {player_id}")
        else:
            print(f"❌ 用户注册失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 用户注册异常: {e}")
        return False
    
    # 3. 测试与每个角色的对话
    print("\n3️⃣ 测试与各角色的LLM对话...")
    
    test_messages = [
        "爱卿，近日朝政如何？",
        "边防告急，你有何良策？"
    ]
    
    success_count = 0
    total_tests = 0
    
    for char in characters:
        char_name = char["name"]
        char_id = str(char["_id"])
        
        print(f"\n--- 测试与{char_name}的对话 ---")
        
        for message in test_messages:
            total_tests += 1
            print(f"\n👑 皇帝: {message}")
            
            try:
                response = requests.post(f"{API_BASE_URL}/api/npc/chat", json={
                    "player_id": player_id,
                    "npc_id": char_id,
                    "message": message
                })
                
                if response.status_code == 200:
                    result = response.json()
                    npc_response = result.get("npc_response", "无回复")
                    npc_mood = result.get("npc_mood", "未知")
                    
                    print(f"🎭 {char_name}: {npc_response[:100]}...")
                    print(f"💭 心情: {npc_mood}")
                    print("✅ 对话成功")
                    success_count += 1
                else:
                    print(f"❌ 对话失败: {response.status_code} - {response.text}")
                    
            except Exception as e:
                print(f"❌ 对话异常: {e}")
            
            # 稍微延迟
            time.sleep(1)
    
    # 4. 测试结果总结
    print(f"\n{'='*60}")
    print("🏁 测试完成！")
    print(f"\n📊 测试统计:")
    print(f"   - 总测试数: {total_tests}")
    print(f"   - 成功数: {success_count}")
    print(f"   - 成功率: {success_count/total_tests*100:.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过！统一角色系统工作完美！")
        return True
    else:
        print(f"\n⚠️ 有 {total_tests - success_count} 个测试失败")
        return False

def test_character_data_structure():
    """测试角色数据结构"""
    print("\n🔍 检查角色数据结构...")
    
    try:
        client = MongoClient("mongodb://localhost:27017/")
        db = client["chongzhen_sim"]
        
        # 获取一个角色的完整数据
        sample_char = db.unified_characters.find_one({"name": "温体仁"})
        client.close()
        
        if sample_char:
            print("✅ 角色数据结构检查:")
            print(f"   - 基本信息: name={sample_char.get('name')}, title={sample_char.get('title')}")
            print(f"   - 游戏属性: {list(sample_char.get('game_stats', {}).keys())}")
            print(f"   - 对话属性: {list(sample_char.get('dialogue_profile', {}).keys())}")
            print(f"   - 忠诚度: {sample_char.get('game_stats', {}).get('loyalty')}")
            print(f"   - 性格: {sample_char.get('dialogue_profile', {}).get('personality', '')[:50]}...")
            return True
        else:
            print("❌ 未找到示例角色数据")
            return False
            
    except Exception as e:
        print(f"❌ 数据结构检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试合并后的统一角色系统")
    
    # 检查数据结构
    if not test_character_data_structure():
        print("❌ 数据结构检查失败，终止测试")
        return
    
    # 测试系统功能
    if test_unified_character_system():
        print("\n🎊 恭喜！数据库合并成功，系统运行正常！")
        print("\n📝 合并成果:")
        print("   ✅ 成功合并 characters 和 npc_profiles 集合")
        print("   ✅ 统一角色模型包含游戏属性和对话属性")
        print("   ✅ LLM对话系统正常工作")
        print("   ✅ 前后端集成无问题")
        print("   ✅ 数据一致性得到保证")
    else:
        print("\n❌ 系统测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
