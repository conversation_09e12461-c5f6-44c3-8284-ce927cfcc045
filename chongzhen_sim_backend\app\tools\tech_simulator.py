"""
技术模拟工具 - 模拟技术发展和创新
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.tools.registry import tool
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class Technology(BaseModel):
    """技术模型"""
    id: str
    name: str
    category: str
    description: str
    prerequisites: List[str] = []
    research_cost: float
    research_time: int  # 回合数
    success_rate: float
    effects: Dict[str, float]
    unlock_conditions: List[str] = []

class TechResearchResult(BaseModel):
    """技术研发结果"""
    technology: Technology
    success: bool
    progress: float
    remaining_time: int
    cost_spent: float
    effects_applied: Dict[str, float]

@tool("tech_simulator", "模拟技术发展")
async def simulate_technology_research(
    tech_name: str,
    investment: float,
    current_tech_level: Dict[str, Any] = None
) -> TechResearchResult:
    """模拟技术研发"""
    
    try:
        # 获取技术信息
        technology = _get_technology_info(tech_name)
        if not technology:
            raise ValueError(f"未知技术: {tech_name}")
        
        # 检查前置条件
        prerequisites_met = _check_prerequisites(technology, current_tech_level or {})
        if not prerequisites_met:
            return TechResearchResult(
                technology=technology,
                success=False,
                progress=0.0,
                remaining_time=technology.research_time,
                cost_spent=0.0,
                effects_applied={}
            )
        
        # 计算研发进度
        progress = min(1.0, investment / technology.research_cost)
        
        # 计算成功率
        base_success_rate = technology.success_rate
        if progress >= 1.0:
            actual_success_rate = base_success_rate
        else:
            actual_success_rate = base_success_rate * progress * 0.8
        
        # 模拟研发结果
        import random
        success = random.random() < actual_success_rate
        
        # 计算剩余时间
        if success:
            remaining_time = 0
        else:
            remaining_time = max(1, int(technology.research_time * (1 - progress)))
        
        # 应用效果
        effects_applied = {}
        if success:
            effects_applied = technology.effects.copy()
        
        result = TechResearchResult(
            technology=technology,
            success=success,
            progress=progress,
            remaining_time=remaining_time,
            cost_spent=min(investment, technology.research_cost),
            effects_applied=effects_applied
        )
        
        logger.info(f"技术研发模拟: {tech_name}, 成功: {success}, 进度: {progress:.2f}")
        return result
        
    except Exception as e:
        logger.error(f"技术模拟失败: {e}")
        # 返回失败结果
        return TechResearchResult(
            technology=Technology(
                id="unknown",
                name=tech_name,
                category="unknown",
                description="未知技术",
                research_cost=10000,
                research_time=5,
                success_rate=0.0,
                effects={}
            ),
            success=False,
            progress=0.0,
            remaining_time=0,
            cost_spent=0.0,
            effects_applied={}
        )

def _get_technology_info(tech_name: str) -> Optional[Technology]:
    """获取技术信息"""
    
    # 预定义的技术树
    technologies = {
        "燧发枪": Technology(
            id="flintlock_musket",
            name="燧发枪",
            category="军事",
            description="改进的火枪，使用燧石点火，比火绳枪更可靠",
            prerequisites=["火绳枪制造"],
            research_cost=50000,
            research_time=6,
            success_rate=0.7,
            effects={
                "military_firepower": 0.3,
                "military_effectiveness": 0.2,
                "army_modernization": 0.25
            },
            unlock_conditions=["接触西洋技术", "有熟练工匠"]
        ),
        "蒸汽机": Technology(
            id="steam_engine",
            name="蒸汽机",
            category="工业",
            description="利用蒸汽推动的机械装置，可用于各种工业用途",
            prerequisites=["金属加工技术", "精密机械"],
            research_cost=100000,
            research_time=12,
            success_rate=0.4,
            effects={
                "industrial_capacity": 0.5,
                "mining_efficiency": 0.3,
                "transportation": 0.2
            },
            unlock_conditions=["西洋科学知识", "大量资金投入"]
        ),
        "改良农具": Technology(
            id="improved_farming_tools",
            name="改良农具",
            category="农业",
            description="改进的犁具和农业工具，提高农业生产效率",
            prerequisites=[],
            research_cost=20000,
            research_time=3,
            success_rate=0.9,
            effects={
                "food_production": 0.15,
                "agricultural_efficiency": 0.2,
                "peasant_satisfaction": 0.1
            },
            unlock_conditions=["工匠支持"]
        ),
        "火炮改进": Technology(
            id="improved_artillery",
            name="火炮改进",
            category="军事",
            description="改进火炮设计，提高射程和威力",
            prerequisites=["铸造技术"],
            research_cost=40000,
            research_time=4,
            success_rate=0.8,
            effects={
                "artillery_power": 0.4,
                "siege_capability": 0.3,
                "military_prestige": 0.1
            },
            unlock_conditions=["军事需求", "技术工匠"]
        ),
        "印刷改进": Technology(
            id="printing_improvement",
            name="印刷改进",
            category="文化",
            description="改进印刷技术，提高书籍印制效率和质量",
            prerequisites=["活字印刷"],
            research_cost=15000,
            research_time=2,
            success_rate=0.85,
            effects={
                "education_efficiency": 0.2,
                "cultural_development": 0.15,
                "administrative_efficiency": 0.1
            },
            unlock_conditions=["文人支持"]
        ),
        "医学发展": Technology(
            id="medical_advancement",
            name="医学发展",
            category="医学",
            description="改进医疗技术和药物制备，提高治疗效果",
            prerequisites=["传统医学"],
            research_cost=30000,
            research_time=5,
            success_rate=0.75,
            effects={
                "population_health": 0.2,
                "plague_resistance": 0.3,
                "population_growth": 0.1
            },
            unlock_conditions=["医学人才", "药材供应"]
        )
    }
    
    return technologies.get(tech_name)

def _check_prerequisites(technology: Technology, current_tech_level: Dict[str, Any]) -> bool:
    """检查技术前置条件"""
    
    if not technology.prerequisites:
        return True
    
    for prerequisite in technology.prerequisites:
        if not current_tech_level.get(prerequisite, False):
            logger.warning(f"技术 {technology.name} 缺少前置条件: {prerequisite}")
            return False
    
    return True

@tool("tech_tree_query", "查询技术树信息")
async def query_tech_tree(category: str = None) -> Dict[str, Any]:
    """查询可用的技术树"""
    
    try:
        # 获取所有技术
        all_techs = [
            "燧发枪", "蒸汽机", "改良农具", "火炮改进", "印刷改进", "医学发展"
        ]
        
        tech_tree = {}
        
        for tech_name in all_techs:
            tech = _get_technology_info(tech_name)
            if tech and (not category or tech.category == category):
                tech_tree[tech_name] = {
                    "category": tech.category,
                    "description": tech.description,
                    "cost": tech.research_cost,
                    "time": tech.research_time,
                    "success_rate": tech.success_rate,
                    "prerequisites": tech.prerequisites,
                    "effects": tech.effects
                }
        
        # 按类别分组
        categories = {}
        for tech_name, tech_info in tech_tree.items():
            cat = tech_info["category"]
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(tech_name)
        
        result = {
            "technologies": tech_tree,
            "categories": categories,
            "total_count": len(tech_tree)
        }
        
        logger.info(f"查询技术树，类别: {category or '全部'}, 数量: {len(tech_tree)}")
        return result
        
    except Exception as e:
        logger.error(f"技术树查询失败: {e}")
        return {
            "technologies": {},
            "categories": {},
            "total_count": 0,
            "error": str(e)
        }

@tool("tech_impact_analyzer", "分析技术影响")
async def analyze_tech_impact(
    tech_name: str,
    current_state: Dict[str, Any]
) -> Dict[str, Any]:
    """分析技术对帝国的潜在影响"""
    
    try:
        technology = _get_technology_info(tech_name)
        if not technology:
            return {"error": f"未知技术: {tech_name}"}
        
        # 分析直接影响
        direct_impacts = {}
        for effect_type, effect_value in technology.effects.items():
            current_value = current_state.get(effect_type, 0.5)
            new_value = current_value + effect_value
            direct_impacts[effect_type] = {
                "current": current_value,
                "change": effect_value,
                "new_value": min(1.0, max(0.0, new_value)),
                "improvement": f"{effect_value * 100:+.1f}%"
            }
        
        # 分析间接影响
        indirect_impacts = _analyze_indirect_impacts(technology, current_state)
        
        # 风险评估
        risks = _assess_tech_risks(technology, current_state)
        
        # 投资建议
        recommendation = _generate_tech_recommendation(technology, current_state)
        
        result = {
            "technology": technology.name,
            "category": technology.category,
            "direct_impacts": direct_impacts,
            "indirect_impacts": indirect_impacts,
            "risks": risks,
            "recommendation": recommendation,
            "cost_benefit_ratio": technology.research_cost / len(technology.effects)
        }
        
        logger.info(f"技术影响分析: {tech_name}")
        return result
        
    except Exception as e:
        logger.error(f"技术影响分析失败: {e}")
        return {"error": str(e)}

def _analyze_indirect_impacts(technology: Technology, current_state: Dict[str, Any]) -> List[str]:
    """分析间接影响"""
    
    impacts = []
    
    if technology.category == "军事":
        impacts.append("可能提升军队战斗力和士气")
        impacts.append("有助于维护边防安全")
        if "firepower" in str(technology.effects):
            impacts.append("可能改变战争形态")
    
    elif technology.category == "工业":
        impacts.append("可能推动其他技术发展")
        impacts.append("有助于提高生产效率")
        impacts.append("可能创造新的就业机会")
    
    elif technology.category == "农业":
        impacts.append("有助于缓解粮食压力")
        impacts.append("可能减少农民负担")
        impacts.append("有利于人口增长")
    
    return impacts

def _assess_tech_risks(technology: Technology, current_state: Dict[str, Any]) -> List[str]:
    """评估技术风险"""
    
    risks = []
    
    # 成本风险
    treasury = current_state.get("treasury", 100000)
    if technology.research_cost > treasury * 0.3:
        risks.append("研发成本较高，可能影响财政")
    
    # 成功率风险
    if technology.success_rate < 0.6:
        risks.append("成功率较低，存在失败风险")
    
    # 时间风险
    if technology.research_time > 8:
        risks.append("研发周期较长，可能错过时机")
    
    # 社会风险
    if technology.category == "军事":
        risks.append("可能引起其他势力警觉")
    elif technology.category == "工业":
        risks.append("可能冲击传统手工业")
    
    return risks

def _generate_tech_recommendation(technology: Technology, current_state: Dict[str, Any]) -> str:
    """生成技术建议"""
    
    treasury = current_state.get("treasury", 100000)
    stability = current_state.get("stability", 0.5)
    
    if technology.research_cost > treasury:
        return "建议暂缓研发，先积累资金"
    
    if stability < 0.4 and technology.category != "军事":
        return "当前局势不稳，建议优先发展军事技术"
    
    if technology.success_rate > 0.8 and technology.research_cost < treasury * 0.2:
        return "强烈推荐立即开始研发"
    
    if technology.success_rate > 0.6:
        return "建议适时投入研发"
    
    return "需要谨慎评估，建议先完善前置条件"
