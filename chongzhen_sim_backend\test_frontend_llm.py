#!/usr/bin/env python3
"""
测试前端LLM对话功能
"""

import requests
import time
from pymongo import MongoClient

# 服务器配置
API_BASE_URL = "http://127.0.0.1:8000"

def get_npc_id_mapping():
    """获取大臣姓名到角色ID的映射（模拟前端函数）"""
    try:
        print('🔍 开始获取NPC ID映射...')
        
        # 从统一角色集合获取角色列表
        client = MongoClient("mongodb://localhost:27017/")
        db = client["chongzhen_sim"]
        
        print('📊 连接MongoDB成功，查询unified_characters集合...')
        characters = list(db.unified_characters.find({}, {"_id": 1, "name": 1}))
        client.close()
        
        print(f'📋 从数据库获取到 {len(characters)} 个角色')
        
        # 构建映射字典
        mapping = {}
        for char in characters:
            char_name = char["name"]
            char_id = str(char["_id"])
            mapping[char_name] = char_id
            print(f'   - {char_name}: {char_id}')
        
        print(f'✅ 角色映射构建完成: {list(mapping.keys())}')
        return mapping
    
    except Exception as e:
        print(f'❌ 获取角色映射失败: {str(e)}')
        import traceback
        print(f'🔍 错误详情: {traceback.format_exc()}')
        return {}

def api_request(endpoint: str, method: str = "GET", data: dict = None):
    """发送API请求（模拟前端函数）"""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        print(f'📤 API请求: {method} {url}')
        if data:
            print(f'📝 请求数据: {data}')

        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data)
        elif method == "PUT":
            response = requests.put(url, json=data)
        elif method == "DELETE":
            response = requests.delete(url)

        print(f'📥 响应状态码: {response.status_code}')

        if response.status_code == 200:
            response_data = response.json()
            print(f'✅ 响应成功: {len(str(response_data))} 字符')
            return response_data
        else:
            error_text = response.text
            print(f'❌ API请求失败: {response.status_code} - {error_text}')
            return None
    except Exception as e:
        print(f'🔌 请求异常: {str(e)}')
        return None

def generate_minister_reply(minister, user_input, player_id):
    """生成大臣回复（模拟前端函数）"""
    try:
        # 首先尝试调用LLM智能对话API
        print(f'🤖 尝试调用LLM对话API，大臣: {minister["name"]}')
        print(f'📝 当前player_id: {player_id}')
        print(f'💬 用户输入: {user_input}')

        # 根据大臣姓名查找对应的NPC ID
        npc_mapping = get_npc_id_mapping()
        npc_id = npc_mapping.get(minister["name"])
        
        print(f'🔍 NPC映射结果: {minister["name"]} -> {npc_id}')

        if npc_id and player_id:
            print(f'✅ 准备调用NPC对话API')
            print(f'   - player_id: {player_id}')
            print(f'   - npc_id: {npc_id}')
            print(f'   - message: {user_input}')
            
            # 调用NPC对话API
            chat_response = api_request("/api/npc/chat", "POST", {
                "player_id": player_id,
                "npc_id": npc_id,
                "message": user_input
            })

            print(f'📥 API响应: {chat_response}')

            if chat_response and "npc_response" in chat_response:
                print(f'✅ LLM对话成功，大臣: {minister["name"]}')
                print(f'🎭 回复内容: {chat_response["npc_response"][:100]}...')
                return chat_response["npc_response"]
            else:
                print(f'⚠️ LLM对话API返回异常，大臣: {minister["name"]}')
                print(f'   响应内容: {chat_response}')
        else:
            if not npc_id:
                print(f'⚠️ 未找到对应NPC ID，大臣: {minister["name"]}')
            if not player_id:
                print(f'⚠️ player_id为空: {player_id}')

    except Exception as e:
        print(f'❌ LLM对话调用失败: {str(e)}')
        import traceback
        print(f'🔍 错误详情: {traceback.format_exc()}')

    # 如果LLM调用失败，回退到简单模拟
    print(f'🔄 回退到简单模拟回复，大臣: {minister["name"]}')
    return f"臣遵旨。（模拟回复）"

def test_frontend_llm():
    """测试前端LLM对话功能"""
    print("🧪 测试前端LLM对话功能")
    print("=" * 60)
    
    # 1. 注册测试用户
    print("\n1️⃣ 注册测试用户...")
    username = f"frontend_test_{int(time.time())}"
    register_response = api_request("/api/user/register", "POST", {
        "username": username,
        "password": "test123",
        "game_difficulty": "normal"
    })
    
    if not register_response or "id" not in register_response:
        print("❌ 用户注册失败，测试终止")
        return
    
    player_id = register_response["id"]
    print(f"✅ 用户注册成功，player_id: {player_id}")
    
    # 2. 测试大臣对话
    print("\n2️⃣ 测试大臣对话...")
    
    # 模拟前端的大臣数据
    test_ministers = [
        {"name": "温体仁", "position": "内阁首辅"},
        {"name": "袁崇焕", "position": "兵部尚书"},
        {"name": "魏忠贤", "position": "司礼监太监"}
    ]
    
    test_message = "爱卿，近日朝政如何？"
    
    for minister in test_ministers:
        print(f"\n--- 测试与{minister['name']}的对话 ---")
        
        reply = generate_minister_reply(minister, test_message, player_id)
        
        print(f"🎭 {minister['name']}回复: {reply}")
        print("=" * 40)
        
        # 稍微延迟
        time.sleep(1)
    
    print("\n🏁 前端LLM对话测试完成！")

if __name__ == "__main__":
    test_frontend_llm()
