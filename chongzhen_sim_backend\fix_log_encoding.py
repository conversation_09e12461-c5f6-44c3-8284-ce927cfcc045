#!/usr/bin/env python3
"""
修复日志文件编码问题
"""

import os
import shutil
from datetime import datetime

def fix_log_encoding():
    """修复日志文件编码"""
    log_file = "logs/chongzhen_sim.log"
    
    if not os.path.exists(log_file):
        print("❌ 日志文件不存在")
        return
    
    # 备份原文件
    backup_file = f"logs/chongzhen_sim.log.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(log_file, backup_file)
    print(f"📦 已备份原日志文件到: {backup_file}")
    
    try:
        # 尝试不同的编码读取文件
        encodings = ['gbk', 'gb2312', 'cp936', 'utf-8', 'latin-1']
        content = None
        used_encoding = None
        
        for encoding in encodings:
            try:
                with open(log_file, 'r', encoding=encoding) as f:
                    content = f.read()
                used_encoding = encoding
                print(f"✅ 成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print("❌ 无法读取日志文件，尝试所有编码都失败")
            return
        
        # 使用UTF-8重新写入文件
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 已将日志文件转换为UTF-8编码")
        
        # 添加编码修复记录
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - SYSTEM - INFO - 日志文件编码已修复为UTF-8\n")
        
        print("📝 已添加编码修复记录")
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        # 恢复备份文件
        shutil.copy2(backup_file, log_file)
        print("🔄 已恢复原文件")

def clear_log_file():
    """清空日志文件（保留UTF-8编码）"""
    log_file = "logs/chongzhen_sim.log"
    
    try:
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - SYSTEM - INFO - 日志文件已清空，编码设置为UTF-8\n")
        print("🗑️ 日志文件已清空并设置为UTF-8编码")
    except Exception as e:
        print(f"❌ 清空日志文件失败: {e}")

def main():
    """主函数"""
    print("🔧 日志编码修复工具")
    print("=" * 40)
    
    choice = input("请选择操作:\n1. 修复现有日志文件编码\n2. 清空日志文件\n请输入选择 (1/2): ")
    
    if choice == "1":
        fix_log_encoding()
    elif choice == "2":
        confirm = input("⚠️ 确定要清空日志文件吗？(y/N): ")
        if confirm.lower() == 'y':
            clear_log_file()
        else:
            print("❌ 操作已取消")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
