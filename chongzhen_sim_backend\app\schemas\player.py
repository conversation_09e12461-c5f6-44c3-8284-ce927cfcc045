"""
玩家相关数据结构
"""

from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any
from datetime import datetime

class PlayerBase(BaseModel):
    """玩家基础信息"""
    username: str
    email: Optional[EmailStr] = None

class PlayerCreate(PlayerBase):
    """创建玩家"""
    password: str
    game_difficulty: Optional[str] = "normal"

class PlayerUpdate(BaseModel):
    """更新玩家信息"""
    email: Optional[EmailStr] = None
    game_notes: Optional[str] = None

class PlayerResponse(PlayerBase):
    """玩家响应信息"""
    id: str
    current_turn: int
    current_year: int
    game_started: datetime
    last_active: datetime
    is_game_active: bool
    game_difficulty: str
    
    # 崇祯个人属性
    emperor_age: int
    emperor_health: float
    emperor_insight: float
    emperor_charisma: float
    emperor_intelligence: float
    
    # 统计信息
    total_edicts_issued: int
    total_ministers_summoned: int
    total_crises_resolved: int
    
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class EmperorAttributes(BaseModel):
    """崇祯个人属性"""
    age: int
    health: float
    insight: float
    charisma: float
    intelligence: float

class GameProgress(BaseModel):
    """游戏进度"""
    current_turn: int
    current_year: int
    years_passed: int
    total_turns: int
    game_duration_days: int
    progress_percentage: float

class PlayerStatistics(BaseModel):
    """玩家统计"""
    total_edicts_issued: int
    total_ministers_summoned: int
    total_crises_resolved: int
    average_edicts_per_year: float

class Achievement(BaseModel):
    """成就"""
    id: str
    name: str
    description: str
    unlocked_at: Optional[datetime] = None
    progress: Optional[float] = None

class PlayerProfile(BaseModel):
    """玩家档案"""
    basic_info: PlayerResponse
    emperor_attributes: EmperorAttributes
    game_progress: GameProgress
    statistics: PlayerStatistics
    achievements: Dict[str, Achievement]
