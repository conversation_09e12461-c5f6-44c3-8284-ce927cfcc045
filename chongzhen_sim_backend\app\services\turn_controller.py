"""
回合控制器 - 管理游戏回合生命周期
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from app.services.agent_orchestrator import AgentOrchestrator
from app.db.models.player import Player
from app.db.managers import player_manager, character_manager
from app.schemas.turn import TurnResult
from app.utils.logger import setup_logger
from app.config import settings
# 临时导入，直接定义类型
from enum import Enum
from typing import List
from pydantic import BaseModel

class TurnPhase(Enum):
    MINISTER_ACTION = "minister_action"
    ENVIRONMENT_ACTION = "environment_action"
    EMPEROR_ACTION = "emperor_action"

class TurnProgressStatus(BaseModel):
    current_phase: TurnPhase
    phase_progress: float
    current_agent: str
    agent_progress: float
    estimated_remaining_seconds: int
    detailed_status: str

class MinisterActionResult(BaseModel):
    minister_id: str
    minister_name: str
    success: bool
    memory_updates: List[str] = []
    dialogue_strategy_updates: dict = {}
    external_actions: List[dict] = []
    resource_changes: dict = {}
    execution_time_seconds: float = 0
    error_message: str = ""

class EnvironmentActionResult(BaseModel):
    success: bool
    edict_simulation_result: dict = {}
    minister_action_simulation_result: dict = {}
    natural_evolution_result: dict = {}
    empire_data_changes: dict = {}
    region_data_changes: dict = {}
    character_data_changes: dict = {}
    system_notifications: List[dict] = []
    minister_memorials: List[dict] = []
    execution_time_seconds: float = 0
    error_message: str = ""

class TurnExecutionResult(BaseModel):
    turn_number: int
    year: int
    month: int
    success: bool
    minister_action_results: List[MinisterActionResult] = []
    environment_action_result: EnvironmentActionResult
    total_execution_time_seconds: float
    next_turn_preview: str
    is_game_over: bool = False
    game_over_reason: str = ""
from app.agents.minister_action_agent import MinisterActionAgent
from app.agents.environment_evolution_agent import EnvironmentEvolutionAgent
from app.agents.notification_agent import NotificationAgent

logger = setup_logger(__name__)

class NewTurnController:
    """新的回合控制器 - 实现三阶段回合演进系统"""

    def __init__(self):
        self.orchestrator = AgentOrchestrator()
        # 使用默认配置
        self.config = type('Config', (), {
            'TURNS_PER_YEAR': 12,
            'MONTHS_PER_TURN': 1
        })()

        # 初始化新的Agent
        self.minister_action_agent = MinisterActionAgent()
        self.environment_evolution_agent = EnvironmentEvolutionAgent()
        self.notification_agent = NotificationAgent()

        # 回合进度状态
        self.current_progress: Optional[TurnProgressStatus] = None
        self.progress_callbacks: List[Callable[[TurnProgressStatus], None]] = []
    
    def add_progress_callback(self, callback: Callable[[TurnProgressStatus], None]):
        """添加进度回调函数"""
        self.progress_callbacks.append(callback)

    def _update_progress(self, phase: TurnPhase, phase_progress: float, agent: str, agent_progress: float, status: str):
        """更新进度状态"""
        self.current_progress = TurnProgressStatus(
            current_phase=phase,
            phase_progress=phase_progress,
            current_agent=agent,
            agent_progress=agent_progress,
            estimated_remaining_seconds=0,  # TODO: 实现时间估算
            detailed_status=status
        )

        # 通知所有回调函数
        for callback in self.progress_callbacks:
            try:
                callback(self.current_progress)
            except Exception as e:
                logger.error(f"进度回调执行失败: {e}")

    async def execute_new_turn(self, player_id: str, edict_content: str) -> TurnExecutionResult:
        """执行新的三阶段回合演进。注意这里的情况是皇帝提交了诏书所以进来了这里，因此这里仅环境+大臣"""
        start_time = time.time()

        try:
            logger.info(f"开始执行新回合演进，玩家ID: {player_id}")

            # 获取基础数据
            player = await self._get_player(player_id)
            empire_state = await self._get_empire_state(player_id)
            regions_state = await self._get_regions_state(player_id)
            ministers_data = await self._get_ministers_data(player_id)
            recent_events = await self._get_recent_events(player_id)

            # 阶段1: 环境行动阶段
            self._update_progress(TurnPhase.ENVIRONMENT_ACTION, 0.0, "environment_evolution_agent", 0.0, "开始环境行动阶段")
            # environment_result = await self._execute_environment_action_phase(
            #     edict_content, minister_results, empire_state, regions_state,
            #     recent_events, player.current_turn, player.current_year
            # )
            environment_result = await self._execute_environment_action_phase(
                edict_content, minister_results, empire_state, regions_state,
                recent_events, player.current_turn, player.current_year
            )
            self._update_progress(TurnPhase.ENVIRONMENT_ACTION, 1.0, "notification_agent", 1.0, "环境行动阶段完成")


            # 阶段2: 大臣行动阶段
            self._update_progress(TurnPhase.MINISTER_ACTION, 0.0, "minister_action_agent", 0.0, "开始大臣行动阶段")
            minister_results = await self._execute_minister_action_phase(
                ministers_data, empire_state, recent_events,
                player.current_turn, player.current_year
            )
            self._update_progress(TurnPhase.MINISTER_ACTION, 1.0, "minister_action_agent", 1.0, "大臣行动阶段完成")

            

            # 更新数据库
            await self._apply_changes_to_database(player_id, environment_result)

            # 推进回合
            await self._advance_turn(player)

            # 构建结果
            execution_time = time.time() - start_time
            result = TurnExecutionResult(
                turn_number=player.current_turn,
                year=player.current_year,
                month=self._calculate_month(player.current_turn),
                success=True,
                minister_action_results=minister_results,
                environment_action_result=environment_result,
                total_execution_time_seconds=execution_time,
                next_turn_preview=self._generate_next_turn_preview(environment_result),
                is_game_over=self._check_game_over_new(environment_result),
                game_over_reason=""
            )

            logger.info(f"新回合演进执行完成，耗时: {execution_time:.2f}秒")
            return result

        except Exception as e:
            logger.error(f"新回合演进执行失败: {e}")
            execution_time = time.time() - start_time
            return TurnExecutionResult(
                turn_number=player.current_turn if 'player' in locals() else 0,
                year=player.current_year if 'player' in locals() else 1,
                month=1,
                success=False,
                minister_action_results=[],
                environment_action_result=EnvironmentActionResult(
                    success=False,
                    error_message=str(e),
                    execution_time_seconds=execution_time
                ),
                total_execution_time_seconds=execution_time,
                next_turn_preview="回合执行失败，请重试",
                is_game_over=False
            )

    async def execute_turn(self, player_id: str, edict_content: str) -> TurnResult:
        """执行一个完整的回合"""
        try:
            logger.info(f"开始执行回合，玩家ID: {player_id}")
            
            # 获取玩家和帝国状态
            player = await self._get_player(player_id)
            empire_state = self._get_empire_state(player_id)
            
            # 检查游戏是否结束
            if self._check_game_over(empire_state):
                return self._create_game_over_result(empire_state)
            
            # 执行回合推演
            turn_result = await self.orchestrator.execute_full_turn(
                player_id=player_id,
                edict_content=edict_content,
                current_state=empire_state,
                current_turn=player.current_turn,
                current_year=player.current_year
            )
            
            # 更新数据库
            self._update_game_state(player_id, turn_result)
            
            # 推进回合
            await self._advance_turn(player)
            
            # 检查是否需要压缩上下文
            if player.current_turn % settings.CONTEXT_COMPRESSION_INTERVAL == 0:
                await self._compress_context(player_id)
            
            logger.info(f"回合执行完成，玩家ID: {player_id}, 回合: {player.current_turn}")
            return turn_result
            
        except Exception as e:
            logger.error(f"回合执行失败: {e}")
            raise

    async def _execute_minister_action_phase(
        self,
        ministers_data: List[Dict[str, Any]],
        empire_state: Dict[str, Any],
        recent_events: List[Dict[str, Any]],
        current_turn: int,
        current_year: int
    ) -> List[MinisterActionResult]:
        """执行大臣行动阶段"""
        try:
            logger.info(f"开始执行大臣行动阶段，大臣数量: {len(ministers_data)}")

            # 获取皇帝近期行动（TODO: 从数据库获取）
            emperor_recent_actions = []

            # 使用大臣行动Agent并行处理所有大臣
            results = await self.minister_action_agent.process_multiple_ministers(
                ministers_data=ministers_data,
                empire_state=empire_state,
                recent_events=recent_events,
                emperor_recent_actions=emperor_recent_actions,
                current_turn=current_turn,
                current_year=current_year
            )

            logger.info(f"大臣行动阶段完成，成功: {sum(1 for r in results if r.success)}/{len(results)}")
            return results

        except Exception as e:
            logger.error(f"大臣行动阶段执行失败: {e}")
            return []

    # cyx TODO 根据刚刚分析的输入输出改这里
    async def _execute_environment_action_phase(
        self,
        edict_content: str,
        minister_results: List[MinisterActionResult],
        empire_state: Dict[str, Any],
        regions_state: List[Dict[str, Any]],
        recent_events: List[Dict[str, Any]],
        current_turn: int,
        current_year: int
    ) -> EnvironmentActionResult:
        """执行环境行动阶段"""
        try:
            logger.info("开始执行环境行动阶段")

            # 1. 环境演化推演
            self._update_progress(TurnPhase.ENVIRONMENT_ACTION, 0.2, "environment_evolution_agent", 0.0, "环境演化推演中")

            from app.agents.environment_evolution_agent import EnvironmentEvolutionInput
            evolution_input = EnvironmentEvolutionInput(
                player_id="",  # TODO: 传入正确的player_id
                edict_content=edict_content,
                minister_action_results=minister_results,
                current_empire_state=empire_state,
                current_regions_state=regions_state,
                current_turn=current_turn,
                current_year=current_year,
                recent_events=recent_events
            )

            evolution_result = await self.environment_evolution_agent.process(evolution_input)

            if not evolution_result.success:
                raise Exception(f"环境演化失败: {evolution_result.error_message}")

            evolution_output = evolution_result.result

            # 2. 生成通知和奏折
            self._update_progress(TurnPhase.ENVIRONMENT_ACTION, 0.7, "notification_agent", 0.0, "生成通知和奏折")

            from app.agents.notification_agent import NotificationInput
            notification_input = NotificationInput(
                player_id="",  # TODO: 传入正确的player_id
                empire_changes=evolution_output.empire_changes,
                region_changes=evolution_output.region_changes,
                new_events=evolution_output.new_events,
                crisis_events=evolution_output.crisis_events,
                minister_action_results=[r.model_dump() for r in minister_results],
                current_turn=current_turn,
                current_year=current_year
            )

            notification_result = await self.notification_agent.process(notification_input)

            if not notification_result.success:
                logger.warning(f"通知生成失败: {notification_result.error_message}")
                notifications = []
                memorials = []
            else:
                notification_output = notification_result.result
                notifications = [n.model_dump() for n in notification_output.system_notifications]
                memorials = [m.model_dump() for m in notification_output.minister_memorials]

            # 构建环境行动结果
            result = EnvironmentActionResult(
                success=True,
                edict_simulation_result=evolution_output.edict_effects,
                minister_action_simulation_result=evolution_output.minister_action_effects,
                natural_evolution_result=evolution_output.natural_evolution_effects,
                empire_data_changes=evolution_output.empire_changes,
                region_data_changes=evolution_output.region_changes,
                character_data_changes={},  # TODO: 实现角色数据变化
                system_notifications=notifications,
                minister_memorials=memorials,
                execution_time_seconds=0  # TODO: 计算实际执行时间
            )

            logger.info("环境行动阶段完成")
            return result

        except Exception as e:
            logger.error(f"环境行动阶段执行失败: {e}")
            return EnvironmentActionResult(
                success=False,
                error_message=str(e),
                execution_time_seconds=0
            )
    
    async def force_simulate_turn(self, player_id: str) -> TurnResult:
        """强制推演一个回合（管理员功能）"""
        try:
            logger.info(f"强制推演回合，玩家ID: {player_id}")
            
            # 使用空指令进行推演
            return await self.execute_turn(player_id, "维持现状，无特殊指令")
            
        except Exception as e:
            logger.error(f"强制推演失败: {e}")
            raise
    
    async def get_game_status(self, player_id: str) -> Dict[str, Any]:
        """获取游戏状态"""
        try:
            player = await self._get_player(player_id)
            empire_state = self._get_empire_state(player_id)
            
            status = {
                "player_info": {
                    "id": player.id,
                    "username": player.username,
                    "current_turn": player.current_turn,
                    "current_year": player.current_year,
                    "game_started": player.game_started,
                    "last_active": player.last_active
                },
                "empire_state": empire_state,
                "game_progress": {
                    "total_turns": player.current_turn,
                    "years_passed": player.current_year - 1,
                    "max_years": settings.MAX_GAME_YEARS,
                    "progress_percentage": (player.current_year - 1) / settings.MAX_GAME_YEARS * 100
                },
                "is_game_over": self._check_game_over(empire_state)
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取游戏状态失败: {e}")
            raise
    
    async def _get_player(self, player_id: str) -> Player:
        """获取玩家信息"""
        from app.db.managers import player_manager
        player = await player_manager.get_player_by_id(player_id)
        if not player:
            raise ValueError(f"玩家不存在: {player_id}")
        return player
    
    def _get_empire_state(self, player_id: str) -> Dict[str, Any]:
        """获取帝国状态"""
        # 这里应该从数据库查询帝国状态
        # 暂时返回模拟数据
        return {
            "treasury": 150000,
            "military_strength": 60000,
            "population": 58000000,
            "food_storage": 800000,
            "military_morale": 0.6,
            "official_loyalty": 0.5,
            "border_stability": 0.4,
            "internal_stability": 0.6,
            "technology_level": {
                "military": 0.3,
                "agriculture": 0.5,
                "industry": 0.2
            }
        }
    
    def _check_game_over(self, empire_state: Dict[str, Any]) -> bool:
        """检查游戏是否结束"""
        # 检查各种游戏结束条件
        
        # 国库破产
        if empire_state.get("treasury", 0) <= 0:
            return True
        
        # 军队全部叛变
        if empire_state.get("military_morale", 1) <= 0.1:
            return True
        
        # 内部完全失控
        if empire_state.get("internal_stability", 1) <= 0.1:
            return True
        
        # 人口锐减
        if empire_state.get("population", 60000000) < 30000000:
            return True
        
        return False
    
    def _create_game_over_result(self, empire_state: Dict[str, Any]) -> TurnResult:
        """创建游戏结束结果"""
        # 分析失败原因
        failure_reasons = []
        
        if empire_state.get("treasury", 0) <= 0:
            failure_reasons.append("国库空虚，财政崩溃")
        
        if empire_state.get("military_morale", 1) <= 0.1:
            failure_reasons.append("军心涣散，军队哗变")
        
        if empire_state.get("internal_stability", 1) <= 0.1:
            failure_reasons.append("内政失控，天下大乱")
        
        if empire_state.get("population", 60000000) < 30000000:
            failure_reasons.append("人口锐减，国力衰竭")
        
        return TurnResult(
            success=False,
            title="大明王朝覆灭",
            content=f"崇祯皇帝，您的统治已经结束。失败原因：{'; '.join(failure_reasons)}。历史将如何评价您的统治？",
            data_changes={},
            next_turn_preview="游戏结束",
            is_game_over=True,
            game_over_reason="; ".join(failure_reasons)
        )
    
    def _update_game_state(self, player_id: str, turn_result: TurnResult):
        """更新游戏状态到数据库"""
        try:
            # 这里应该更新数据库中的帝国状态
            # 暂时只记录日志
            logger.info(f"更新游戏状态，玩家ID: {player_id}")
            
            # TODO: 实现数据库更新逻辑
            # 1. 更新Empire表中的各项数值
            # 2. 记录回合历史
            # 3. 更新角色关系和忠诚度
            # 4. 保存推演结果到记忆系统
            
        except Exception as e:
            logger.error(f"更新游戏状态失败: {e}")
            raise
    
    async def _advance_turn(self, player: Player):
        """推进回合"""
        try:
            player.current_turn += 1

            # 每12个回合（月）推进一年
            if player.current_turn % getattr(settings, 'MAX_TURNS_PER_YEAR', 12) == 0:
                player.current_year += 1

            # 更新最后活跃时间
            from datetime import datetime, timezone
            player.last_active = datetime.now(timezone.utc)

            # 使用MongoDB管理器更新玩家
            from app.db.managers import player_manager
            await player_manager.update_player(player.id, {
                "current_turn": player.current_turn,
                "current_year": player.current_year,
                "last_active": player.last_active
            })

            logger.info(f"回合推进：玩家 {player.id}, 回合 {player.current_turn}, 年份 {player.current_year}")

        except Exception as e:
            logger.error(f"回合推进失败: {e}")
            raise
    
    async def _compress_context(self, player_id: str):
        """压缩上下文（每5年执行一次）"""
        try:
            logger.info(f"开始压缩上下文，玩家ID: {player_id}")
            
            # TODO: 实现上下文压缩逻辑
            # 1. 获取过去5年的所有推演记录
            # 2. 使用LLM总结重要事件和变化
            # 3. 保留关键信息，删除冗余细节
            # 4. 更新记忆系统
            
            logger.info(f"上下文压缩完成，玩家ID: {player_id}")
            
        except Exception as e:
            logger.error(f"上下文压缩失败: {e}")
            # 压缩失败不应该影响游戏继续

    # 新增的辅助方法
    async def _get_empire_state(self, player_id: str) -> Dict[str, Any]:
        """获取帝国状态"""
        # TODO: 从数据库查询真实的帝国状态
        return {
            "treasury": 50000,
            "food_storage": 100000,
            "total_military": 200000,
            "population": 60000000,
            "internal_stability": 0.6,
            "military_morale": 0.7,
            "foreign_relations": "紧张"
        }

    async def _get_regions_state(self, player_id: str) -> List[Dict[str, Any]]:
        """获取地区状态"""
        # TODO: 从数据库查询真实的地区状态
        return [
            {
                "id": "beijing",
                "name": "北京",
                "population": 1200000,
                "stability": 0.8,
                "prosperity": 0.8,
                "loyalty": 0.9
            },
            {
                "id": "liaodong",
                "name": "辽东",
                "population": 300000,
                "stability": 0.4,
                "prosperity": 0.3,
                "loyalty": 0.6
            }
        ]

    async def _get_ministers_data(self, player_id: str) -> List[Dict[str, Any]]:
        """获取大臣数据"""
        # TODO: 从数据库查询真实的大臣数据
        return [
            {
                "id": "wenren",
                "name": "温体仁",
                "title": "内阁首辅",
                "age": 55,
                "game_stats": {
                    "loyalty": 0.7,
                    "administrative_ability": 0.6,
                    "military_ability": 0.3,
                    "economic_ability": 0.5,
                    "diplomatic_ability": 0.4,
                    "faction": "阉党",
                    "political_stance": "保守",
                    "personal_wealth": 50000,
                    "current_location": "京师"
                }
            }
        ]

    async def _get_recent_events(self, player_id: str) -> List[Dict[str, Any]]:
        """获取近期事件"""
        # TODO: 从数据库查询近期事件
        return []

    async def _apply_changes_to_database(self, player_id: str, environment_result: EnvironmentActionResult):
        """将变化应用到数据库"""
        try:
            logger.info(f"开始应用数据变化到数据库，玩家ID: {player_id}")

            # TODO: 实现数据库更新逻辑
            # 1. 更新帝国数据
            # 2. 更新地区数据
            # 3. 更新角色数据
            # 4. 保存事件记录
            # 5. 保存通知和奏折

            logger.info("数据变化应用完成")

        except Exception as e:
            logger.error(f"应用数据变化失败: {e}")
            raise

    def _calculate_month(self, turn: int) -> int:
        """根据回合数计算月份"""
        return ((turn - 1) % self.config.TURNS_PER_YEAR) + 1

    def _generate_next_turn_preview(self, environment_result: EnvironmentActionResult) -> str:
        """生成下回合预览"""
        if not environment_result.success:
            return "环境推演失败，下回合情况未知"

        preview_parts = []

        # 基于危机事件生成预览
        if environment_result.crisis_events:
            crisis_count = len([e for e in environment_result.crisis_events if e.get('severity', 0) >= 4])
            if crisis_count > 0:
                preview_parts.append(f"预计将面临{crisis_count}个重大危机")

        # 基于数据变化生成预览
        empire_changes = environment_result.empire_data_changes
        if empire_changes.get('treasury', 0) < -10000:
            preview_parts.append("国库状况堪忧")
        if empire_changes.get('internal_stability', 0) < -0.1:
            preview_parts.append("内政稳定度下降")

        if not preview_parts:
            preview_parts.append("帝国运转正常")

        return "下回合" + "，".join(preview_parts) + "。"

    def _check_game_over_new(self, environment_result: EnvironmentActionResult) -> bool:
        """检查游戏是否结束（新版本）"""
        if not environment_result.success:
            return False

        # 检查是否有游戏结束级别的危机
        for crisis in environment_result.crisis_events:
            if crisis.get('severity', 0) >= 5 and crisis.get('type') == 'game_over':
                return True

        # 检查帝国数据是否达到游戏结束条件
        empire_changes = environment_result.empire_data_changes
        if empire_changes.get('treasury', 0) <= -100000:  # 国库严重透支
            return True
        if empire_changes.get('internal_stability', 1) <= 0.1:  # 内政完全失控
            return True

        return False
