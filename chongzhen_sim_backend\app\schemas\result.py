"""
结果相关数据结构
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any

class GameResult(BaseModel):
    """游戏结果"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    errors: Optional[List[str]] = None

class TurnExecutionResult(BaseModel):
    """回合执行结果"""
    turn: int
    year: int
    edict_summary: str
    execution_success: bool
    major_changes: List[str]
    data_changes: Dict[str, Any]
    next_turn_preview: str
    warnings: Optional[List[str]] = None

class SimulationResult(BaseModel):
    """模拟结果"""
    narrative: str
    events: List[Dict[str, Any]]
    data_changes: Dict[str, Any]
    success: bool
    warnings: Optional[List[str]] = None

class CharacterInteractionResult(BaseModel):
    """角色互动结果"""
    character_name: str
    response: str
    mood: str
    loyalty_change: float
    relationship_status: str

class CrisisResult(BaseModel):
    """危机处理结果"""
    crisis_name: str
    resolution_success: bool
    impact_description: str
    long_term_effects: Dict[str, Any]
    lessons_learned: Optional[str] = None

class GameOverResult(BaseModel):
    """游戏结束结果"""
    final_year: int
    total_turns: int
    cause_of_end: str
    final_assessment: str
    achievements: List[str]
    historical_impact: str
    final_score: Optional[int] = None
