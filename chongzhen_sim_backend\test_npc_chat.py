#!/usr/bin/env python3
"""
测试NPC对话功能
"""

import requests
import json
import asyncio
from pymongo import MongoClient

# 服务器配置
BASE_URL = "http://127.0.0.1:8000"

def get_npc_list():
    """获取NPC列表"""
    try:
        # 连接MongoDB获取NPC列表
        client = MongoClient("mongodb://localhost:27017/")
        db = client["chongzhen_sim"]
        npcs = list(db.npc_profiles.find({}, {"_id": 1, "name": 1, "title": 1}))
        client.close()
        
        print("可用的NPC:")
        for npc in npcs:
            print(f"- ID: {npc['_id']}, 姓名: {npc['name']}, 职位: {npc['title']}")
        
        return npcs
    except Exception as e:
        print(f"获取NPC列表失败: {e}")
        return []

def register_test_user():
    """注册测试用户"""
    try:
        import time
        username = f"test_emperor_{int(time.time())}"  # 使用时间戳确保唯一性
        response = requests.post(f"{BASE_URL}/api/user/register", json={
            "username": username,
            "password": "test123",
            "game_difficulty": "normal"
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"用户注册成功: {result}")
            return result.get("id")  # 使用id字段作为player_id
        else:
            print(f"用户注册失败: {response.status_code}, {response.text}")
            return None
    except Exception as e:
        print(f"注册用户时出错: {e}")
        return None

def test_npc_chat(player_id, npc_id, message):
    """测试NPC对话"""
    try:
        response = requests.post(f"{BASE_URL}/api/npc/chat", json={
            "player_id": player_id,
            "npc_id": npc_id,
            "message": message
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n=== NPC对话测试成功 ===")
            print(f"皇帝: {message}")
            print(f"NPC回复: {result['npc_response']}")
            print(f"NPC心情: {result['npc_mood']}")
            print(f"会话ID: {result['session_id']}")
            print(f"对话ID: {result['conversation_id']}")
            return result
        else:
            print(f"NPC对话失败: {response.status_code}, {response.text}")
            return None
    except Exception as e:
        print(f"测试NPC对话时出错: {e}")
        return None

def main():
    """主测试函数"""
    print("=== 崇祯模拟器 NPC对话系统测试 ===\n")
    
    # 1. 获取NPC列表
    npcs = get_npc_list()
    if not npcs:
        print("无法获取NPC列表，退出测试")
        return
    
    # 2. 注册测试用户
    player_id = register_test_user()
    if not player_id:
        print("无法注册测试用户，退出测试")
        return
    
    # 3. 测试与第一个NPC对话
    npc = npcs[0]
    npc_id = str(npc["_id"])
    npc_name = npc["name"]
    
    print(f"\n开始与 {npc_name} 对话...")
    
    # 测试多轮对话
    test_messages = [
        "爱卿，近日朝政如何？",
        "朕对边防颇为担忧，你有何良策？",
        "很好，就按你说的办。"
    ]
    
    session_id = None
    for i, message in enumerate(test_messages, 1):
        print(f"\n--- 第{i}轮对话 ---")
        
        # 构建请求数据
        chat_data = {
            "player_id": player_id,
            "npc_id": npc_id,
            "message": message
        }
        
        # 如果有会话ID，继续使用
        if session_id:
            chat_data["session_id"] = session_id
        
        result = test_npc_chat(player_id, npc_id, message)
        if result:
            session_id = result["session_id"]
        else:
            print("对话失败，停止测试")
            break
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
