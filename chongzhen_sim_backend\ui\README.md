# 崇祯模拟器 Web UI

基于 Streamlit 的崇祯模拟器前端界面，提供直观的游戏体验。

## 🚀 快速开始

### 1. 安装依赖

```bash
cd ui
pip install -r requirements.txt
```

### 2. 启动应用

#### 方法一：使用启动脚本（推荐）
```bash
python run_ui.py
```

#### 方法二：直接使用 Streamlit
```bash
streamlit run main.py --server.port 8501
```

### 3. 访问应用

打开浏览器访问：http://localhost:8501

## 📱 功能特性

### 🏠 主界面
- 游戏概览仪表板
- 当前回合和年份显示
- 最近事件展示
- 快速回合推进

### 👤 崇祯个人数据
- 皇帝基本信息（年龄、健康、能力等）
- 游戏统计数据
- 能力雷达图可视化

### 🏛️ 大明势力数据
- 国库、粮食、军队、人口等资源状况
- 资源变化趋势图表
- 实时数据监控

### 🗺️ 地图系统
- 各地区详细信息展示
- 人口、稳定度、繁荣度、忠诚度指标
- 地区对比图表
- 鼠标悬停查看详情

### 👥 召见大臣
- 可召见大臣列表
- 大臣忠诚度和能力显示
- 实时对话系统
- 派系信息展示

### 📜 诏书写作工具
- 多种诏书模板选择
- AI 智能建议系统
- 诏书润色功能
- 影响预测展示
- 历史诏书查看

## 🎨 界面特色

- **古典风格**：采用明朝风格的配色和设计
- **响应式布局**：适配不同屏幕尺寸
- **交互式图表**：使用 Plotly 提供丰富的数据可视化
- **实时更新**：与后端 API 实时同步数据

## 🔧 配置说明

### API 配置
在 `config.py` 中修改 API 基础 URL：
```python
API_BASE_URL = "http://127.0.0.1:8000"
```

### 主题配置
可以在 `config.py` 中自定义颜色主题：
```python
COLORS = {
    "primary": "#8B4513",
    "secondary": "#CD853F",
    # ...
}
```

### 数据配置
默认的地区、大臣、诏书模板等数据都可以在 `config.py` 中修改。

### 日志配置
前端包含详细的日志系统，记录所有用户操作和API交互：

```python
from logger import log_ui_event

# 记录不同类型的事件
log_ui_event('api', 'API请求', {'url': '/api/test'})
log_ui_event('auth', '用户登录', {'username': 'user'})
log_ui_event('nav', '页面导航', {'page': '主界面'})
```

支持的日志类型：
- `api` - API请求和响应
- `auth` - 认证相关操作
- `nav` - 页面导航
- `chat` - 大臣对话
- `edict` - 诏书操作
- `success` - 成功操作
- `warning` - 警告信息
- `error` - 错误信息

## 📋 依赖说明

- **streamlit**: Web 应用框架
- **requests**: HTTP 请求库
- **plotly**: 交互式图表库
- **pandas**: 数据处理库
- **numpy**: 数值计算库

## 🐛 故障排除

### 1. 无法连接后端
- 确保后端服务器正在运行（默认端口 8000）
- 检查 `config.py` 中的 API_BASE_URL 配置

### 2. 依赖安装失败
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### 3. 端口被占用
修改启动命令中的端口：
```bash
streamlit run main.py --server.port 8502
```

## 🔄 开发模式

启用开发模式以获得更好的调试体验：
```bash
streamlit run main.py --server.runOnSave true
```

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 实现基础功能模块
- 支持登录注册
- 完整的游戏界面

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

MIT License
