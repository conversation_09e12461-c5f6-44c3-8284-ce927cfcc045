"""
前端日志配置
"""

import logging
import sys
from datetime import datetime

def setup_ui_logger():
    """设置UI日志"""
    # 创建logger
    logger = logging.getLogger('chongzhen_ui')
    logger.setLevel(logging.INFO)
    
    # 避免重复添加handler
    if logger.handlers:
        return logger
    
    # 创建控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - 🎮 UI - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(formatter)
    
    # 添加handler到logger
    logger.addHandler(console_handler)
    
    return logger

def log_ui_event(event_type: str, message: str, data: dict = None):
    """记录UI事件"""
    logger = setup_ui_logger()
    
    # 事件类型图标映射
    icons = {
        'api': '🌐',
        'auth': '🔐',
        'nav': '📄',
        'chat': '💬',
        'edict': '📜',
        'error': '❌',
        'success': '✅',
        'warning': '⚠️',
        'info': 'ℹ️'
    }
    
    icon = icons.get(event_type, '📝')
    log_message = f"{icon} {message}"
    
    if data:
        log_message += f" | 数据: {data}"
    
    logger.info(log_message)

# 全局logger实例
ui_logger = setup_ui_logger()
