"""
资源计算工具 - 计算财政、军事、人口等资源变化
"""

import re
from typing import Dict, Any, List, Tuple
from pydantic import BaseModel
from app.tools.registry import tool
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class ResourceChange(BaseModel):
    """资源变化"""
    resource_type: str
    old_value: float
    new_value: float
    change_amount: float
    change_reason: str

class ResourceCalculationResult(BaseModel):
    """资源计算结果"""
    changes: List[ResourceChange]
    total_cost: float
    feasibility: bool
    warnings: List[str]

@tool("resource_calculator", "计算资源变化")
async def calculate_changes(
    edict_content: str, 
    current_state: Dict[str, Any]
) -> ResourceCalculationResult:
    """计算指令执行后的资源变化"""
    
    changes = []
    total_cost = 0.0
    warnings = []
    
    try:
        # 解析当前状态
        current_treasury = current_state.get("treasury", 100000)  # 银两
        current_military = current_state.get("military_strength", 50000)  # 兵力
        current_population = current_state.get("population", 60000000)  # 人口
        current_food = current_state.get("food_storage", 1000000)  # 粮食储备
        current_morale = current_state.get("military_morale", 0.7)  # 军心
        current_loyalty = current_state.get("official_loyalty", 0.6)  # 官员忠诚
        
        # 分析财政影响
        financial_changes = _calculate_financial_impact(edict_content, current_treasury)
        changes.extend(financial_changes["changes"])
        total_cost += financial_changes["cost"]
        warnings.extend(financial_changes["warnings"])
        
        # 分析军事影响
        military_changes = _calculate_military_impact(edict_content, current_military, current_morale)
        changes.extend(military_changes["changes"])
        total_cost += military_changes["cost"]
        warnings.extend(military_changes["warnings"])
        
        # 分析人口影响
        population_changes = _calculate_population_impact(edict_content, current_population)
        changes.extend(population_changes["changes"])
        warnings.extend(population_changes["warnings"])
        
        # 分析粮食影响
        food_changes = _calculate_food_impact(edict_content, current_food)
        changes.extend(food_changes["changes"])
        warnings.extend(food_changes["warnings"])
        
        # 分析政治影响
        political_changes = _calculate_political_impact(edict_content, current_loyalty)
        changes.extend(political_changes["changes"])
        warnings.extend(political_changes["warnings"])
        
        # 检查可行性
        feasibility = total_cost <= current_treasury
        if not feasibility:
            warnings.append(f"所需银两 {total_cost} 超过国库存银 {current_treasury}")
        
        result = ResourceCalculationResult(
            changes=changes,
            total_cost=total_cost,
            feasibility=feasibility,
            warnings=warnings
        )
        
        logger.info(f"资源计算完成，总成本: {total_cost}, 可行性: {feasibility}")
        return result
        
    except Exception as e:
        logger.error(f"资源计算失败: {e}")
        return ResourceCalculationResult(
            changes=[],
            total_cost=0.0,
            feasibility=False,
            warnings=[f"计算失败: {str(e)}"]
        )

def _calculate_financial_impact(content: str, current_treasury: float) -> Dict[str, Any]:
    """计算财政影响"""
    changes = []
    cost = 0.0
    warnings = []
    
    # 支出项目
    expenditure_patterns = {
        r"赈灾|救济": (50000, "赈灾支出"),
        r"军饷|发饷": (30000, "军饷支出"),
        r"修建|建设|工程": (100000, "基建支出"),
        r"采购|购买|装备": (20000, "采购支出"),
        r"奖赏|赏赐": (10000, "奖赏支出"),
        r"大规模.*征兵": (80000, "征兵支出"),
        r"研发|开发": (40000, "研发支出")
    }
    
    for pattern, (base_cost, reason) in expenditure_patterns.items():
        if re.search(pattern, content):
            # 根据规模调整成本
            scale_multiplier = 1.0
            if "大规模" in content or "全面" in content:
                scale_multiplier = 2.0
            elif "小规模" in content or "少量" in content:
                scale_multiplier = 0.5
            
            actual_cost = base_cost * scale_multiplier
            cost += actual_cost
            
            changes.append(ResourceChange(
                resource_type="treasury",
                old_value=current_treasury,
                new_value=current_treasury - actual_cost,
                change_amount=-actual_cost,
                change_reason=reason
            ))
    
    # 收入项目
    income_patterns = {
        r"增税|加税": (20000, "增税收入"),
        r"开征.*税": (15000, "新税收入"),
        r"盐税|商税": (10000, "专项税收"),
        r"没收|抄家": (30000, "没收收入")
    }
    
    for pattern, (base_income, reason) in income_patterns.items():
        if re.search(pattern, content):
            changes.append(ResourceChange(
                resource_type="treasury",
                old_value=current_treasury,
                new_value=current_treasury + base_income,
                change_amount=base_income,
                change_reason=reason
            ))
            cost -= base_income  # 收入减少总成本
    
    # 财政警告
    if cost > current_treasury * 0.5:
        warnings.append("此次支出将消耗国库一半以上银两")
    
    if "减税" in content and current_treasury < 100000:
        warnings.append("国库银两不足，减税可能导致财政危机")
    
    return {
        "changes": changes,
        "cost": cost,
        "warnings": warnings
    }

def _calculate_military_impact(content: str, current_military: float, current_morale: float) -> Dict[str, Any]:
    """计算军事影响"""
    changes = []
    cost = 0.0
    warnings = []
    
    # 军力变化
    if re.search(r"征兵|招募", content):
        if "大规模" in content:
            troop_increase = 20000
            recruitment_cost = 60000
        else:
            troop_increase = 10000
            recruitment_cost = 30000
        
        changes.append(ResourceChange(
            resource_type="military_strength",
            old_value=current_military,
            new_value=current_military + troop_increase,
            change_amount=troop_increase,
            change_reason="征兵增加兵力"
        ))
        cost += recruitment_cost
    
    # 士气变化
    morale_changes = {
        r"发放.*军饷|补发.*饷": (0.1, "发放军饷提升士气"),
        r"严厉.*处罚|军法.*处置": (-0.05, "严厉处罚影响士气"),
        r"奖赏.*将士|嘉奖": (0.08, "奖赏提升士气"),
        r"训练|操练": (0.05, "训练提升士气"),
        r"败仗|失败": (-0.15, "败仗影响士气")
    }
    
    for pattern, (morale_change, reason) in morale_changes.items():
        if re.search(pattern, content):
            new_morale = max(0, min(1, current_morale + morale_change))
            changes.append(ResourceChange(
                resource_type="military_morale",
                old_value=current_morale,
                new_value=new_morale,
                change_amount=morale_change,
                change_reason=reason
            ))
    
    # 军事警告
    if "主动出击" in content and current_morale < 0.5:
        warnings.append("军心不稳，主动出击风险较大")
    
    if "大规模征兵" in content and current_military > 80000:
        warnings.append("兵力已经较多，继续征兵可能造成财政负担")
    
    return {
        "changes": changes,
        "cost": cost,
        "warnings": warnings
    }

def _calculate_population_impact(content: str, current_population: float) -> Dict[str, Any]:
    """计算人口影响"""
    changes = []
    warnings = []
    
    # 人口变化因素
    population_factors = {
        r"赈灾|救济": (10000, "赈灾减少人口流失"),
        r"战争|征战": (-5000, "战争造成人口损失"),
        r"瘟疫|疫病": (-20000, "疫病造成人口损失"),
        r"迁移|移民": (0, "人口迁移"),
        r"屠城|屠杀": (-50000, "屠杀造成人口锐减")
    }
    
    for pattern, (population_change, reason) in population_factors.items():
        if re.search(pattern, content):
            if population_change != 0:
                changes.append(ResourceChange(
                    resource_type="population",
                    old_value=current_population,
                    new_value=current_population + population_change,
                    change_amount=population_change,
                    change_reason=reason
                ))
    
    # 人口警告
    if current_population < 50000000:
        warnings.append("人口已经较少，需要注意保护民力")
    
    return {
        "changes": changes,
        "warnings": warnings
    }

def _calculate_food_impact(content: str, current_food: float) -> Dict[str, Any]:
    """计算粮食影响"""
    changes = []
    warnings = []
    
    # 粮食变化
    food_factors = {
        r"开仓放粮|赈济": (-100000, "开仓放粮消耗储备"),
        r"征收.*粮食|收粮": (50000, "征收增加储备"),
        r"军粮|军队.*粮食": (-30000, "军队消耗粮食"),
        r"灾害|旱灾|水灾": (-80000, "自然灾害影响粮食"),
        r"丰收|好收成": (120000, "丰收增加储备")
    }
    
    for pattern, (food_change, reason) in food_factors.items():
        if re.search(pattern, content):
            changes.append(ResourceChange(
                resource_type="food_storage",
                old_value=current_food,
                new_value=current_food + food_change,
                change_amount=food_change,
                change_reason=reason
            ))
    
    # 粮食警告
    if current_food < 500000:
        warnings.append("粮食储备不足，需要及时补充")
    
    return {
        "changes": changes,
        "warnings": warnings
    }

def _calculate_political_impact(content: str, current_loyalty: float) -> Dict[str, Any]:
    """计算政治影响"""
    changes = []
    warnings = []
    
    # 忠诚度变化
    loyalty_factors = {
        r"提拔|升迁|任命": (0.05, "提拔官员提升忠诚"),
        r"罢免|贬谪|处罚": (-0.1, "处罚官员影响忠诚"),
        r"改革|变法": (-0.05, "改革可能引起反对"),
        r"奖赏.*官员": (0.08, "奖赏提升忠诚"),
        r"严查.*贪腐": (-0.03, "反腐可能引起不满"),
        r"减税|惠民": (0.03, "惠民政策提升支持")
    }
    
    for pattern, (loyalty_change, reason) in loyalty_factors.items():
        if re.search(pattern, content):
            new_loyalty = max(0, min(1, current_loyalty + loyalty_change))
            changes.append(ResourceChange(
                resource_type="official_loyalty",
                old_value=current_loyalty,
                new_value=new_loyalty,
                change_amount=loyalty_change,
                change_reason=reason
            ))
    
    # 政治警告
    if current_loyalty < 0.4:
        warnings.append("官员忠诚度较低，需要谨慎处理政治事务")
    
    return {
        "changes": changes,
        "warnings": warnings
    }
