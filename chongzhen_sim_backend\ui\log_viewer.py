#!/usr/bin/env python3
"""
前端日志查看器
实时显示前端操作日志
"""

import streamlit as st
import time
import os
from datetime import datetime
from logger import log_ui_event

def main():
    """日志查看器主界面"""
    st.set_page_config(
        page_title="崇祯模拟器 - 日志查看器",
        page_icon="📊",
        layout="wide"
    )
    
    st.title("📊 崇祯模拟器前端日志查看器")
    
    # 侧边栏控制
    with st.sidebar:
        st.header("🔧 控制面板")
        
        # 自动刷新控制
        auto_refresh = st.checkbox("自动刷新", value=True)
        refresh_interval = st.slider("刷新间隔(秒)", 1, 10, 3)
        
        # 日志级别过滤
        st.subheader("📋 日志过滤")
        show_api = st.checkbox("API请求", value=True)
        show_auth = st.checkbox("认证操作", value=True)
        show_nav = st.checkbox("页面导航", value=True)
        show_chat = st.checkbox("大臣对话", value=True)
        show_edict = st.checkbox("诏书操作", value=True)
        show_success = st.checkbox("成功信息", value=True)
        show_warning = st.checkbox("警告信息", value=True)
        show_error = st.checkbox("错误信息", value=True)
        
        # 清空日志
        if st.button("🗑️ 清空显示"):
            st.session_state.log_messages = []
    
    # 初始化日志消息列表
    if 'log_messages' not in st.session_state:
        st.session_state.log_messages = []
    
    # 模拟日志消息（实际使用中会从日志文件或内存中读取）
    if st.button("🧪 生成测试日志"):
        test_logs = [
            ("api", "GET /api/user/profile", {"status": 200}),
            ("auth", "用户登录成功", {"username": "test_user"}),
            ("nav", "导航到主界面", {"from": "登录页"}),
            ("chat", "召见温体仁", {"minister": "温体仁"}),
            ("edict", "提交诏书", {"length": 150}),
            ("success", "操作完成"),
            ("warning", "网络延迟较高"),
            ("error", "API请求失败", {"error": "timeout"})
        ]
        
        for log_type, message, data in test_logs:
            timestamp = datetime.now().strftime("%H:%M:%S")
            st.session_state.log_messages.append({
                "timestamp": timestamp,
                "type": log_type,
                "message": message,
                "data": data if len(test_logs) > 2 else None
            })
    
    # 显示日志
    st.subheader("📜 实时日志")
    
    # 创建日志显示区域
    log_container = st.container()
    
    with log_container:
        if not st.session_state.log_messages:
            st.info("暂无日志信息。请在主界面进行操作或点击'生成测试日志'查看示例。")
        else:
            # 过滤日志
            filtered_logs = []
            for log in st.session_state.log_messages:
                log_type = log["type"]
                if (
                    (log_type == "api" and show_api) or
                    (log_type == "auth" and show_auth) or
                    (log_type == "nav" and show_nav) or
                    (log_type == "chat" and show_chat) or
                    (log_type == "edict" and show_edict) or
                    (log_type == "success" and show_success) or
                    (log_type == "warning" and show_warning) or
                    (log_type == "error" and show_error)
                ):
                    filtered_logs.append(log)
            
            # 显示过滤后的日志
            for log in reversed(filtered_logs[-50:]):  # 只显示最近50条
                timestamp = log["timestamp"]
                log_type = log["type"]
                message = log["message"]
                data = log.get("data")
                
                # 根据日志类型选择图标和颜色
                icons = {
                    'api': '🌐',
                    'auth': '🔐',
                    'nav': '📄',
                    'chat': '💬',
                    'edict': '📜',
                    'success': '✅',
                    'warning': '⚠️',
                    'error': '❌'
                }
                
                colors = {
                    'api': '#2196F3',
                    'auth': '#4CAF50',
                    'nav': '#FF9800',
                    'chat': '#9C27B0',
                    'edict': '#795548',
                    'success': '#4CAF50',
                    'warning': '#FF9800',
                    'error': '#F44336'
                }
                
                icon = icons.get(log_type, '📝')
                color = colors.get(log_type, '#666666')
                
                # 构建日志显示内容
                log_content = f"**{timestamp}** {icon} {message}"
                if data:
                    log_content += f" `{data}`"
                
                # 使用不同颜色显示
                st.markdown(
                    f'<div style="padding: 8px; margin: 4px 0; border-left: 4px solid {color}; background-color: rgba(0,0,0,0.05);">'
                    f'{log_content}'
                    f'</div>',
                    unsafe_allow_html=True
                )
    
    # 统计信息
    st.subheader("📊 日志统计")
    if st.session_state.log_messages:
        col1, col2, col3, col4 = st.columns(4)
        
        total_logs = len(st.session_state.log_messages)
        api_logs = len([l for l in st.session_state.log_messages if l["type"] == "api"])
        error_logs = len([l for l in st.session_state.log_messages if l["type"] == "error"])
        success_logs = len([l for l in st.session_state.log_messages if l["type"] == "success"])
        
        col1.metric("总日志数", total_logs)
        col2.metric("API请求", api_logs)
        col3.metric("错误数", error_logs)
        col4.metric("成功数", success_logs)
    
    # 自动刷新
    if auto_refresh:
        time.sleep(refresh_interval)
        st.rerun()

if __name__ == "__main__":
    main()
