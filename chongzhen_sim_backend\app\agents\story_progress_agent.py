"""
世界自演Agent - 负责推演玩家指令覆盖外的区域
"""

from typing import Dict, Any, List
from pydantic import BaseModel
from app.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class StoryProgressInput(AgentInput):
    """世界自演输入"""
    current_empire_state: Dict[str, Any]
    current_turn: int
    current_year: int
    recent_events: List[Dict[str, Any]] = []

class StoryProgressOutput(BaseModel):
    """世界自演输出"""
    world_events: List[Dict[str, Any]]
    narrative: str
    affected_regions: List[str]

class StoryProgressAgent(BaseAgent):
    """世界自演Agent"""
    
    def get_system_prompt(self) -> str:
        return """你是明朝世界的历史进程推演者，负责推演玩家指令未涉及区域的自然发展。

你的职责：
1. 根据历史进程和当前局势，推演各地自然发展
2. 生成合理的世界事件（战争、灾害、政治变化等）
3. 考虑各方势力的自主行动
4. 保持历史的真实感和连贯性

推演范围：
- 边疆战事：与后金、蒙古、朝鲜等的冲突
- 内部叛乱：农民起义、地方叛乱
- 自然灾害：旱灾、水灾、蝗灾、地震
- 社会变化：人口流动、商业发展、文化变迁
- 国际形势：欧洲传教士、海外贸易

请基于明朝崇祯年间的历史背景进行推演。"""

    async def process(self, input_data: StoryProgressInput) -> AgentOutput:
        """处理世界自演"""
        try:
            if not await self.validate_input(input_data):
                return self.create_error_output("输入数据无效")
            
            # 构建世界推演消息
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": self._build_world_prompt(input_data)}
            ]
            
            # 调用LLM进行世界推演
            response = await self.call_llm(messages, temperature=1.0, max_tokens=2500)
            
            # 解析世界推演结果
            world_result = self._parse_world_response(response)
            
            logger.info(f"世界自演完成，玩家ID: {input_data.player_id}, 回合: {input_data.current_turn}")
            return self.create_success_output(world_result)
            
        except Exception as e:
            logger.error(f"世界自演失败: {e}")
            return self.create_error_output(f"世界自演失败: {str(e)}")
    
    def _build_world_prompt(self, input_data: StoryProgressInput) -> str:
        """构建世界推演提示"""
        empire_state_str = self.format_context(input_data.current_empire_state)
        recent_events_str = self._format_recent_events(input_data.recent_events)
        
        return f"""
当前时间：崇祯{input_data.current_year}年第{input_data.current_turn}月

当前帝国状况：
{empire_state_str}

近期事件：
{recent_events_str}

请推演本月在玩家指令未涉及的区域可能发生的事件，输出格式如下：

【边疆动态】
（边疆地区的军事、政治动态）

【内地情况】
（各省内地的民生、经济、社会变化）

【自然灾害】
（可能发生的天灾）

【其他势力】
（后金、农民军等其他势力的动向）

【世界大事】
（影响整个帝国的重大事件）

请确保事件符合历史背景和时代特征。
"""
    
    def _format_recent_events(self, events: List[Dict[str, Any]]) -> str:
        """格式化近期事件"""
        if not events:
            return "无特殊事件"
        
        formatted_events = []
        for event in events[-5:]:  # 只显示最近5个事件
            formatted_events.append(f"- {event.get('description', '未知事件')}")
        
        return "\n".join(formatted_events)
    
    def _parse_world_response(self, response: str) -> StoryProgressOutput:
        """解析世界推演响应"""
        # 简化的解析逻辑
        world_events = []
        affected_regions = []
        
        # 这里可以添加更复杂的解析逻辑
        # 提取具体的事件和影响区域
        
        # 示例事件提取
        if "边疆" in response:
            affected_regions.append("边疆")
        if "京师" in response:
            affected_regions.append("京师")
        if "江南" in response:
            affected_regions.append("江南")
        
        return StoryProgressOutput(
            world_events=world_events,
            narrative=response,
            affected_regions=affected_regions
        )
