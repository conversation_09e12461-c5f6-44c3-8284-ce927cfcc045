"""
配置项管理
"""

import os
from typing import List
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "崇祯模拟器"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    MONGODB_URL: str = "mongodb://localhost:27017"
    DATABASE_NAME: str = "chongzhen_sim"
    
    # LLM模型配置
    OPENAI_API_KEY: str = "sk-FYHWhPEuIvF65GBsFTH9324QpAZb9iWCNOsNCwkBx7AQkxAJ"
    OPENAI_BASE_URL: str = "https://aigc-api.apps-hangyan.danlu.netease.com/v1"
    DEFAULT_MODEL: str = "qwen-plus"
    LLM_MAX_TOKENS: int = 4096
    LLM_TEMPERATURE: float = 0.7
    
    # 本地模型配置（可选）
    LOCAL_MODEL_PATH: str = ""
    LOCAL_MODEL_API_URL: str = ""
    
    # 向量数据库配置
    VECTOR_DB_URL: str = "http://localhost:6333"
    VECTOR_COLLECTION_NAME: str = "chongzhen_memory"
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # 游戏配置
    MAX_TURNS_PER_YEAR: int = 12  # 每年12个月
    CONTEXT_COMPRESSION_INTERVAL: int = 60  # 每5年压缩一次上下文
    MAX_GAME_YEARS: int = 40  # 最大游戏年限

    # 回合系统配置
    TURN_MONTHS_PER_TURN: int = 1  # 每回合代表的月数
    TURN_MINISTER_PARALLEL: bool = True  # 大臣行动是否并行
    TURN_ENVIRONMENT_SERIAL: bool = True  # 环境行动是否串行
    TURN_MAX_CONCURRENT_MINISTERS: int = 10  # 最大并发大臣数
    TURN_ACTION_TIMEOUT: int = 30  # 单个行动超时时间（秒）
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/chongzhen_sim.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 全局配置实例
settings = Settings()
