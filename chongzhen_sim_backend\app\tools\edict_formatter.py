"""
指令格式化工具 - 格式化和验证玩家指令
"""

import re
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.tools.registry import tool
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class FormattedEdict(BaseModel):
    """格式化后的指令"""
    military_section: str = ""
    domestic_section: str = ""
    financial_section: str = ""
    special_section: str = ""
    raw_content: str = ""
    is_valid: bool = True
    validation_errors: List[str] = []

@tool("edict_formatter", "格式化和验证玩家指令")
async def format_edict(raw_edict: str, current_state: Dict[str, Any] = None) -> FormattedEdict:
    """格式化玩家指令"""
    
    formatted_edict = FormattedEdict(raw_content=raw_edict)
    
    try:
        # 基础清理
        cleaned_edict = _clean_text(raw_edict)
        
        # 分析指令内容
        sections = _analyze_edict_content(cleaned_edict)
        
        formatted_edict.military_section = sections.get("military", "")
        formatted_edict.domestic_section = sections.get("domestic", "")
        formatted_edict.financial_section = sections.get("financial", "")
        formatted_edict.special_section = sections.get("special", "")
        
        # 验证指令
        validation_result = _validate_edict(formatted_edict, current_state)
        formatted_edict.is_valid = validation_result["is_valid"]
        formatted_edict.validation_errors = validation_result["errors"]
        
        logger.info(f"指令格式化完成，有效性: {formatted_edict.is_valid}")
        return formatted_edict
        
    except Exception as e:
        logger.error(f"指令格式化失败: {e}")
        formatted_edict.is_valid = False
        formatted_edict.validation_errors = [f"格式化失败: {str(e)}"]
        return formatted_edict

def _clean_text(text: str) -> str:
    """清理文本"""
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除特殊字符（保留中文、英文、数字、基本标点）
    text = re.sub(r'[^\u4e00-\u9fff\w\s.,;:!?()（），。；：！？]', '', text)
    
    return text

def _analyze_edict_content(content: str) -> Dict[str, str]:
    """分析指令内容，分类到不同部分"""
    
    sections = {
        "military": "",
        "domestic": "",
        "financial": "",
        "special": ""
    }
    
    # 军事相关关键词
    military_keywords = [
        "军队", "士兵", "将军", "战争", "边防", "军饷", "武器", "训练",
        "征兵", "调兵", "出征", "防守", "攻击", "军营", "军官", "战略"
    ]
    
    # 内政相关关键词
    domestic_keywords = [
        "官员", "任命", "罢免", "政策", "法律", "制度", "改革", "治理",
        "民政", "教育", "科举", "监察", "司法", "行政", "地方", "州县"
    ]
    
    # 财政相关关键词
    financial_keywords = [
        "银两", "税收", "国库", "财政", "赋税", "商税", "盐税", "关税",
        "开支", "支出", "收入", "预算", "经济", "贸易", "货币", "物价"
    ]
    
    # 特殊指令关键词
    special_keywords = [
        "技术", "发明", "创新", "工业", "火器", "蒸汽", "机械", "科学",
        "实验", "研发", "制造", "工厂", "新式", "西洋", "传教士"
    ]
    
    # 分句处理
    sentences = re.split(r'[。！？；]', content)
    
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
            
        # 计算每个类别的匹配度
        military_score = sum(1 for keyword in military_keywords if keyword in sentence)
        domestic_score = sum(1 for keyword in domestic_keywords if keyword in sentence)
        financial_score = sum(1 for keyword in financial_keywords if keyword in sentence)
        special_score = sum(1 for keyword in special_keywords if keyword in sentence)
        
        # 分配到得分最高的类别
        max_score = max(military_score, domestic_score, financial_score, special_score)
        
        if max_score == 0:
            # 如果没有匹配，默认分配到内政
            sections["domestic"] += sentence + "。"
        elif military_score == max_score:
            sections["military"] += sentence + "。"
        elif domestic_score == max_score:
            sections["domestic"] += sentence + "。"
        elif financial_score == max_score:
            sections["financial"] += sentence + "。"
        else:
            sections["special"] += sentence + "。"
    
    return sections

def _validate_edict(edict: FormattedEdict, current_state: Dict[str, Any] = None) -> Dict[str, Any]:
    """验证指令的合理性"""
    
    errors = []
    
    # 检查是否有内容
    total_content = (
        edict.military_section + 
        edict.domestic_section + 
        edict.financial_section + 
        edict.special_section
    ).strip()
    
    if not total_content:
        errors.append("指令内容为空")
        return {"is_valid": False, "errors": errors}
    
    # 检查指令长度
    if len(total_content) > 5000:
        errors.append("指令内容过长，请精简")
    
    if len(total_content) < 10:
        errors.append("指令内容过短，请详细说明")
    
    # 检查资源约束
    if current_state:
        treasury = current_state.get("treasury", 0)
        
        # 检查财政指令是否超出能力
        if edict.financial_section:
            if "大量银两" in edict.financial_section and treasury < 0.3:
                errors.append("国库银两不足，无法执行大额支出指令")
            
            if "减税" in edict.financial_section and treasury < 0.5:
                errors.append("当前财政状况不允许减税")
        
        # 检查军事指令是否合理
        if edict.military_section:
            military_strength = current_state.get("military_strength", 0.5)
            
            if "大规模征兵" in edict.military_section and treasury < 0.4:
                errors.append("财政不足以支持大规模征兵")
            
            if "主动出击" in edict.military_section and military_strength < 0.3:
                errors.append("当前军力不足以支持主动出击")
    
    # 检查危险指令
    dangerous_patterns = [
        r"杀.*所有",
        r"屠.*城",
        r"灭.*族",
        r"处死.*大臣"
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, total_content):
            errors.append("指令包含过于极端的内容，请重新考虑")
    
    return {
        "is_valid": len(errors) == 0,
        "errors": errors
    }

@tool("edict_complexity_analyzer", "分析指令复杂度")
async def analyze_edict_complexity(edict: FormattedEdict) -> Dict[str, Any]:
    """分析指令的复杂度和执行难度"""
    
    complexity_result = {
        "overall_complexity": 0,
        "section_complexity": {},
        "execution_difficulty": "简单",
        "estimated_turns": 1,
        "resource_requirements": {}
    }
    
    sections = {
        "military": edict.military_section,
        "domestic": edict.domestic_section,
        "financial": edict.financial_section,
        "special": edict.special_section
    }
    
    total_complexity = 0
    
    for section_name, section_content in sections.items():
        if not section_content:
            continue
            
        # 计算单个部分的复杂度
        section_complexity = _calculate_section_complexity(section_content)
        complexity_result["section_complexity"][section_name] = section_complexity
        total_complexity += section_complexity
    
    complexity_result["overall_complexity"] = total_complexity
    
    # 确定执行难度
    if total_complexity <= 3:
        complexity_result["execution_difficulty"] = "简单"
        complexity_result["estimated_turns"] = 1
    elif total_complexity <= 6:
        complexity_result["execution_difficulty"] = "中等"
        complexity_result["estimated_turns"] = 2
    elif total_complexity <= 10:
        complexity_result["execution_difficulty"] = "困难"
        complexity_result["estimated_turns"] = 3
    else:
        complexity_result["execution_difficulty"] = "极其困难"
        complexity_result["estimated_turns"] = 4
    
    return complexity_result

def _calculate_section_complexity(content: str) -> int:
    """计算单个部分的复杂度"""
    if not content:
        return 0
    
    complexity = 0
    
    # 基础复杂度（基于长度）
    complexity += min(3, len(content) // 100)
    
    # 复杂操作关键词
    complex_keywords = [
        "改革", "重组", "建立", "创新", "研发", "大规模", "全面",
        "彻底", "根本", "系统", "整顿", "变革", "革新"
    ]
    
    for keyword in complex_keywords:
        if keyword in content:
            complexity += 1
    
    # 涉及多个领域
    if "同时" in content or "并且" in content:
        complexity += 1
    
    # 时间要求
    if "立即" in content or "马上" in content:
        complexity += 1
    
    return min(5, complexity)  # 最大复杂度为5
