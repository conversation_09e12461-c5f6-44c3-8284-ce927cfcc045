"""
工具注册中心 - 管理所有可用工具
"""

from typing import Dict, Any, Callable, Optional
from functools import wraps
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

# 全局工具注册表
_tool_registry: Dict[str, Callable] = {}

def tool(name: str, description: str = ""):
    """工具装饰器"""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                logger.debug(f"调用工具: {name}")
                result = await func(*args, **kwargs)
                logger.debug(f"工具 {name} 执行成功")
                return result
            except Exception as e:
                logger.error(f"工具 {name} 执行失败: {e}")
                raise
        
        # 添加元数据
        wrapper._tool_name = name
        wrapper._tool_description = description
        
        # 注册工具
        _tool_registry[name] = wrapper
        logger.info(f"注册工具: {name} - {description}")
        
        return wrapper
    return decorator

def get_tool(name: str) -> Optional[Callable]:
    """获取工具"""
    return _tool_registry.get(name)

def list_tools() -> Dict[str, str]:
    """列出所有工具"""
    return {
        name: getattr(func, '_tool_description', '')
        for name, func in _tool_registry.items()
    }

def register_tool(name: str, func: Callable, description: str = ""):
    """手动注册工具"""
    func._tool_name = name
    func._tool_description = description
    _tool_registry[name] = func
    logger.info(f"手动注册工具: {name} - {description}")

# 导入所有工具模块以触发注册
def initialize_tools():
    """初始化所有工具"""
    try:
        from . import crisis_generator
        from . import edict_formatter
        from . import resource_calculator
        from . import map_query
        from . import tech_simulator
        logger.info("所有工具初始化完成")
    except Exception as e:
        logger.error(f"工具初始化失败: {e}")

# 自动初始化
initialize_tools()
