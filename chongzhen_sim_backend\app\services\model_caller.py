"""
模型调用服务 - 统一封装LLM调用
"""

import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
from app.config import settings
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class ModelCaller:
    """LLM模型调用器"""
    
    def __init__(self):
        self.openai_api_key = settings.OPENAI_API_KEY
        self.openai_base_url = settings.OPENAI_BASE_URL
        self.default_model = settings.DEFAULT_MODEL
        self.local_model_url = settings.LOCAL_MODEL_API_URL
    
    async def call_model(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        use_local: bool = False
    ) -> str:
        """调用LLM模型"""
        
        try:
            if use_local and self.local_model_url:
                return await self._call_local_model(
                    messages, temperature, max_tokens
                )
            else:
                return await self._call_openai_model(
                    messages, model or self.default_model, temperature, max_tokens
                )
                
        except Exception as e:
            logger.error(f"模型调用失败: {e}")
            # 尝试备用方案
            if not use_local and self.local_model_url:
                logger.info("尝试使用本地模型作为备用")
                try:
                    return await self._call_local_model(
                        messages, temperature, max_tokens
                    )
                except Exception as local_e:
                    logger.error(f"本地模型调用也失败: {local_e}")
            
            raise Exception(f"所有模型调用方案都失败: {e}")
    
    async def _call_openai_model(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float,
        max_tokens: int
    ) -> str:
        """调用OpenAI API"""
        
        if not self.openai_api_key:
            raise Exception("OpenAI API Key未配置")
        
        headers = {
            "Authorization": f"Bearer {self.openai_api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.openai_base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"OpenAI API调用失败: {response.status}, {error_text}")
                
                result = await response.json()
                
                if "choices" not in result or not result["choices"]:
                    raise Exception("OpenAI API返回格式异常")
                
                content = result["choices"][0]["message"]["content"]
                
                # 记录token使用情况
                if "usage" in result:
                    usage = result["usage"]
                    logger.info(f"OpenAI API调用成功，使用tokens: {usage}")
                
                return content.strip()
    
    async def _call_local_model(
        self,
        messages: List[Dict[str, str]],
        temperature: float,
        max_tokens: int
    ) -> str:
        """调用本地模型"""
        
        if not self.local_model_url:
            raise Exception("本地模型URL未配置")
        
        # 构建本地模型请求格式（可能需要根据具体模型调整）
        payload = {
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": False
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.local_model_url}/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=120)
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"本地模型调用失败: {response.status}, {error_text}")
                
                result = await response.json()
                
                # 根据本地模型的返回格式解析（可能需要调整）
                if "choices" in result and result["choices"]:
                    content = result["choices"][0]["message"]["content"]
                elif "response" in result:
                    content = result["response"]
                else:
                    raise Exception("本地模型返回格式异常")
                
                logger.info("本地模型调用成功")
                return content.strip()
    
    async def call_model_with_retry(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        temperature: float = 0.7,
        max_tokens: int = 2000,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> str:
        """带重试的模型调用"""
        
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                logger.debug(f"模型调用尝试 {attempt + 1}/{max_retries}")
                
                result = await self.call_model(
                    messages=messages,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
                
                return result
                
            except Exception as e:
                last_exception = e
                logger.warning(f"模型调用尝试 {attempt + 1} 失败: {e}")
                
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (2 ** attempt))  # 指数退避
        
        raise Exception(f"模型调用重试 {max_retries} 次后仍然失败: {last_exception}")
    
    async def batch_call_model(
        self,
        batch_requests: List[Dict[str, Any]],
        max_concurrent: int = 5
    ) -> List[str]:
        """批量调用模型"""
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def call_single(request: Dict[str, Any]) -> str:
            async with semaphore:
                return await self.call_model(**request)
        
        try:
            tasks = [call_single(request) for request in batch_requests]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"批量调用第 {i} 个请求失败: {result}")
                    processed_results.append(f"调用失败: {str(result)}")
                else:
                    processed_results.append(result)
            
            return processed_results
            
        except Exception as e:
            logger.error(f"批量模型调用失败: {e}")
            raise
    
    def estimate_tokens(self, text: str) -> int:
        """估算文本的token数量（粗略估算）"""
        # 简单的token估算：中文字符约1.5个token，英文单词约1个token
        chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
        other_chars = len(text) - chinese_chars
        
        estimated_tokens = int(chinese_chars * 1.5 + other_chars * 0.25)
        return estimated_tokens
    
    def truncate_messages(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int = 8000
    ) -> List[Dict[str, str]]:
        """截断消息以适应token限制"""
        
        total_tokens = 0
        truncated_messages = []
        
        # 从后往前保留消息，确保最新的消息被保留
        for message in reversed(messages):
            message_tokens = self.estimate_tokens(message.get("content", ""))
            
            if total_tokens + message_tokens > max_tokens:
                # 如果是第一条消息且超长，进行截断
                if not truncated_messages:
                    content = message.get("content", "")
                    # 保留后半部分内容
                    truncated_content = content[len(content)//2:]
                    truncated_messages.append({
                        "role": message.get("role", "user"),
                        "content": f"...(前文省略)...{truncated_content}"
                    })
                break
            
            total_tokens += message_tokens
            truncated_messages.append(message)
        
        # 恢复原始顺序
        return list(reversed(truncated_messages))
    
    async def health_check(self) -> Dict[str, bool]:
        """检查模型服务健康状态"""
        
        health_status = {
            "openai": False,
            "local": False
        }
        
        # 检查OpenAI
        if self.openai_api_key:
            try:
                test_messages = [{"role": "user", "content": "Hello"}]
                await self._call_openai_model(test_messages, self.default_model, 0.1, 10)
                health_status["openai"] = True
            except Exception as e:
                logger.warning(f"OpenAI健康检查失败: {e}")
        
        # 检查本地模型
        if self.local_model_url:
            try:
                test_messages = [{"role": "user", "content": "Hello"}]
                await self._call_local_model(test_messages, 0.1, 10)
                health_status["local"] = True
            except Exception as e:
                logger.warning(f"本地模型健康检查失败: {e}")
        
        return health_status
