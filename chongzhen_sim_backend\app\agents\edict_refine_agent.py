"""
诏书润色Agent - 负责润色和格式化玩家的指令
"""

from typing import Dict, Any
from pydantic import BaseModel
from app.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class EdictRefineInput(AgentInput):
    """诏书润色输入"""
    raw_edict: str
    current_situation: Dict[str, Any] = {}

class EdictRefineOutput(BaseModel):
    """诏书润色输出"""
    refined_edict: str
    military_section: str
    domestic_section: str
    financial_section: str
    special_instructions: str

class EdictRefineAgent(BaseAgent):
    """诏书润色Agent"""
    
    def get_system_prompt(self) -> str:
        return """你是明朝崇祯皇帝的内阁首辅，负责润色皇帝的诏书。

你的职责：
1. 将皇帝的现代语言转换为符合明朝文风的诏书
2. 确保诏书格式规范，包含军事、内政、财政三个部分
3. 保持皇帝原意的同时，使语言更加庄重威严
4. 对于不合理或危险的指令，给出委婉的建议

诏书格式要求：
- 军事部分：涉及军队调动、战争、边防等
- 内政部分：涉及官员任免、政策改革、民生等  
- 财政部分：涉及税收、国库、赈灾等
- 特殊指令：涉及技术发展、特殊政策等

请保持明朝的历史背景和语言风格。"""

    async def process(self, input_data: EdictRefineInput) -> AgentOutput:
        """处理诏书润色"""
        try:
            if not await self.validate_input(input_data):
                return self.create_error_output("输入数据无效")
            
            # 构建消息
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": self._build_refine_prompt(input_data)}
            ]
            
            # 调用LLM
            response = await self.call_llm(messages, temperature=0.8)
            
            # 解析响应
            refined_output = self._parse_response(response)
            
            logger.info(f"诏书润色完成，玩家ID: {input_data.player_id}")
            return self.create_success_output(refined_output)
            
        except Exception as e:
            logger.error(f"诏书润色失败: {e}")
            return self.create_error_output(f"诏书润色失败: {str(e)}")
    
    def _build_refine_prompt(self, input_data: EdictRefineInput) -> str:
        """构建润色提示"""
        context_str = self.format_context(input_data.current_situation)
        
        return f"""
当前朝廷情况：
{context_str}

皇帝原始指令：
{input_data.raw_edict}

请将上述指令润色为正式的诏书，并按以下格式输出：

【军事部分】
（如有军事相关内容）

【内政部分】  
（如有内政相关内容）

【财政部分】
（如有财政相关内容）

【特殊指令】
（如有特殊或创新性指令）

请确保：
1. 语言符合明朝诏书风格
2. 保持皇帝原意
3. 对不合理指令给出建议
4. 格式清晰规范
"""
    
    def _parse_response(self, response: str) -> EdictRefineOutput:
        """解析LLM响应"""
        # 简单的解析逻辑，实际可能需要更复杂的处理
        sections = {
            "military_section": "",
            "domestic_section": "",
            "financial_section": "",
            "special_instructions": ""
        }
        
        current_section = None
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if '【军事部分】' in line:
                current_section = "military_section"
            elif '【内政部分】' in line:
                current_section = "domestic_section"
            elif '【财政部分】' in line:
                current_section = "financial_section"
            elif '【特殊指令】' in line:
                current_section = "special_instructions"
            elif current_section and line:
                sections[current_section] += line + "\n"
        
        return EdictRefineOutput(
            refined_edict=response,
            **sections
        )
