"""
Agent基类 - 定义所有Agent的通用接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.services.model_caller import ModelCaller
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class AgentInput(BaseModel):
    """Agent输入基类"""
    player_id: str  # 修改为字符串类型，与数据库一致
    context: Dict[str, Any] = {}
    
class AgentOutput(BaseModel):
    """Agent输出基类"""
    success: bool
    result: Any
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = {}

class BaseAgent(ABC):
    """Agent基类"""
    
    def __init__(self, model_caller: ModelCaller = None):
        self.model_caller = model_caller or ModelCaller()
        self.agent_name = self.__class__.__name__
        
    @abstractmethod
    async def process(self, input_data: AgentInput) -> AgentOutput:
        """处理输入并返回输出"""
        pass
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        pass
    
    async def call_llm(
        self, 
        messages: List[Dict[str, str]], 
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> str:
        """调用LLM"""
        try:
            response = await self.model_caller.call_model(
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response
        except Exception as e:
            logger.error(f"{self.agent_name} LLM调用失败: {e}")
            raise
    
    def format_context(self, context: Dict[str, Any]) -> str:
        """格式化上下文信息"""
        formatted_parts = []
        for key, value in context.items():
            if isinstance(value, dict):
                formatted_parts.append(f"{key}: {self._format_dict(value)}")
            elif isinstance(value, list):
                formatted_parts.append(f"{key}: {self._format_list(value)}")
            else:
                formatted_parts.append(f"{key}: {value}")
        return "\n".join(formatted_parts)
    
    def _format_dict(self, data: dict, indent: int = 1) -> str:
        """格式化字典"""
        lines = []
        for k, v in data.items():
            prefix = "  " * indent
            if isinstance(v, dict):
                lines.append(f"{prefix}{k}:")
                lines.append(self._format_dict(v, indent + 1))
            else:
                lines.append(f"{prefix}{k}: {v}")
        return "\n".join(lines)
    
    def _format_list(self, data: list) -> str:
        """格式化列表"""
        return "\n".join([f"- {item}" for item in data])
    
    async def validate_input(self, input_data: AgentInput) -> bool:
        """验证输入数据"""
        return True
    
    def create_success_output(self, result: Any, metadata: Dict[str, Any] = None) -> AgentOutput:
        """创建成功输出"""
        return AgentOutput(
            success=True,
            result=result,
            metadata=metadata or {}
        )
    
    def create_error_output(self, error_message: str, metadata: Dict[str, Any] = None) -> AgentOutput:
        """创建错误输出"""
        return AgentOutput(
            success=False,
            result=None,
            error_message=error_message,
            metadata=metadata or {}
        )
