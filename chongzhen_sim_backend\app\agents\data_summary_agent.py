"""
数值总结Agent - 负责从推演结果中提取和总结数值变化
"""

from typing import Dict, Any, List
from pydantic import BaseModel
from app.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class DataSummaryInput(AgentInput):
    """数值总结输入"""
    simulation_result: str
    world_events: str
    current_data: Dict[str, Any]

class DataSummaryOutput(BaseModel):
    """数值总结输出"""
    financial_changes: Dict[str, float]
    military_changes: Dict[str, float]
    population_changes: Dict[str, float]
    loyalty_changes: Dict[str, float]
    resource_changes: Dict[str, float]
    summary: str

class DataSummaryAgent(BaseAgent):
    """数值总结Agent"""
    
    def get_system_prompt(self) -> str:
        return """你是明朝帝国的数据分析官，负责从推演结果中提取准确的数值变化。

你的职责：
1. 从推演文本中识别所有数值变化
2. 将变化分类到不同的数据类别
3. 确保数值变化的合理性和一致性
4. 生成数值变化的总结报告

数据类别：
- 财政：国库银两、税收、支出
- 军事：兵力、士气、装备
- 人口：各地人口变化、流民
- 忠诚：官员忠诚度、民心
- 资源：粮食、武器、其他物资

输出格式要求：
- 所有数值变化用正负数表示
- 百分比变化用小数表示（如0.1表示10%增长）
- 绝对数值变化用具体数字
- 提供变化原因说明"""

    async def process(self, input_data: DataSummaryInput) -> AgentOutput:
        """处理数值总结"""
        try:
            if not await self.validate_input(input_data):
                return self.create_error_output("输入数据无效")
            
            # 构建数值分析消息
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": self._build_analysis_prompt(input_data)}
            ]
            
            # 调用LLM进行数值分析
            response = await self.call_llm(messages, temperature=0.3, max_tokens=2000)
            
            # 解析数值变化
            summary_result = self._parse_data_changes(response, input_data.current_data)
            
            logger.info(f"数值总结完成，玩家ID: {input_data.player_id}")
            return self.create_success_output(summary_result)
            
        except Exception as e:
            logger.error(f"数值总结失败: {e}")
            return self.create_error_output(f"数值总结失败: {str(e)}")
    
    def _build_analysis_prompt(self, input_data: DataSummaryInput) -> str:
        """构建数值分析提示"""
        current_data_str = self.format_context(input_data.current_data)
        
        return f"""
当前帝国数据：
{current_data_str}

推演结果：
{input_data.simulation_result}

世界事件：
{input_data.world_events}

请从上述推演结果中提取所有数值变化，按以下格式输出：

【财政变化】
国库银两变化: +/-数值 (原因)
税收变化: +/-数值 (原因)
支出变化: +/-数值 (原因)

【军事变化】
兵力变化: +/-数值 (原因)
士气变化: +/-百分比 (原因)
装备变化: +/-数值 (原因)

【人口变化】
总人口变化: +/-数值 (原因)
流民变化: +/-数值 (原因)

【忠诚度变化】
官员忠诚度: +/-百分比 (原因)
民心变化: +/-百分比 (原因)

【资源变化】
粮食储备: +/-数值 (原因)
武器库存: +/-数值 (原因)

请确保所有数值变化都有明确的原因说明。
"""
    
    def _parse_data_changes(self, response: str, current_data: Dict[str, Any]) -> DataSummaryOutput:
        """解析数值变化"""
        # 初始化变化字典
        financial_changes = {}
        military_changes = {}
        population_changes = {}
        loyalty_changes = {}
        resource_changes = {}
        
        # 简化的解析逻辑 - 实际应该更复杂
        lines = response.split('\n')
        current_category = None
        
        for line in lines:
            line = line.strip()
            if '【财政变化】' in line:
                current_category = 'financial'
            elif '【军事变化】' in line:
                current_category = 'military'
            elif '【人口变化】' in line:
                current_category = 'population'
            elif '【忠诚度变化】' in line:
                current_category = 'loyalty'
            elif '【资源变化】' in line:
                current_category = 'resource'
            elif ':' in line and current_category:
                # 解析具体的数值变化
                parts = line.split(':')
                if len(parts) >= 2:
                    key = parts[0].strip()
                    value_part = parts[1].strip()
                    
                    # 提取数值（简化处理）
                    try:
                        if '+' in value_part or '-' in value_part:
                            # 提取数值
                            import re
                            numbers = re.findall(r'[+-]?\d+\.?\d*', value_part)
                            if numbers:
                                value = float(numbers[0])
                                
                                if current_category == 'financial':
                                    financial_changes[key] = value
                                elif current_category == 'military':
                                    military_changes[key] = value
                                elif current_category == 'population':
                                    population_changes[key] = value
                                elif current_category == 'loyalty':
                                    loyalty_changes[key] = value
                                elif current_category == 'resource':
                                    resource_changes[key] = value
                    except:
                        continue
        
        return DataSummaryOutput(
            financial_changes=financial_changes,
            military_changes=military_changes,
            population_changes=population_changes,
            loyalty_changes=loyalty_changes,
            resource_changes=resource_changes,
            summary=response
        )
