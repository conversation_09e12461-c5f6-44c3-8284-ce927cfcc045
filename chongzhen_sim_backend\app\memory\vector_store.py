"""
向量存储管理
"""

from typing import List, Dict, Any, Optional
import asyncio
from app.config import settings
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class VectorStore:
    """向量存储基类"""
    
    def __init__(self):
        self.collection_name = settings.VECTOR_COLLECTION_NAME
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档"""
        raise NotImplementedError
    
    async def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索相似文档"""
        raise NotImplementedError
    
    async def delete_documents(self, document_ids: List[str]) -> bool:
        """删除文档"""
        raise NotImplementedError
    
    async def update_document(self, document_id: str, document: Dict[str, Any]) -> bool:
        """更新文档"""
        raise NotImplementedError

class QdrantVectorStore(VectorStore):
    """Qdrant向量存储实现"""
    
    def __init__(self):
        super().__init__()
        self.client = None
        self.vector_size = 1536  # OpenAI embedding size
    
    async def _get_client(self):
        """获取Qdrant客户端"""
        if self.client is None:
            try:
                from qdrant_client import QdrantClient
                from qdrant_client.models import Distance, VectorParams, PointStruct
                
                self.client = QdrantClient(url=settings.VECTOR_DB_URL)
                
                # 创建集合（如果不存在）
                try:
                    await self.client.create_collection(
                        collection_name=self.collection_name,
                        vectors_config=VectorParams(
                            size=self.vector_size,
                            distance=Distance.COSINE
                        )
                    )
                    logger.info(f"创建向量集合: {self.collection_name}")
                except Exception as e:
                    if "already exists" not in str(e):
                        logger.error(f"创建向量集合失败: {e}")
                        raise
                
            except ImportError:
                logger.warning("Qdrant客户端未安装，向量存储功能不可用")
                return None
            except Exception as e:
                logger.error(f"连接Qdrant失败: {e}")
                return None
        
        return self.client
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档到向量存储"""
        try:
            client = await self._get_client()
            if not client:
                return False
            
            from qdrant_client.models import PointStruct
            
            points = []
            for i, doc in enumerate(documents):
                # 生成向量（这里需要调用embedding API）
                vector = await self._generate_embedding(doc.get("content", ""))
                if not vector:
                    continue
                
                point = PointStruct(
                    id=doc.get("id", f"doc_{i}"),
                    vector=vector,
                    payload=doc
                )
                points.append(point)
            
            if points:
                await client.upsert(
                    collection_name=self.collection_name,
                    points=points
                )
                logger.info(f"添加 {len(points)} 个文档到向量存储")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"添加文档到向量存储失败: {e}")
            return False
    
    async def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索相似文档"""
        try:
            client = await self._get_client()
            if not client:
                return []
            
            # 生成查询向量
            query_vector = await self._generate_embedding(query)
            if not query_vector:
                return []
            
            # 执行搜索
            search_result = await client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=limit
            )
            
            results = []
            for hit in search_result:
                result = hit.payload.copy()
                result["score"] = hit.score
                results.append(result)
            
            logger.info(f"向量搜索返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"向量搜索失败: {e}")
            return []
    
    async def _generate_embedding(self, text: str) -> Optional[List[float]]:
        """生成文本向量"""
        try:
            # 这里应该调用OpenAI的embedding API
            # 暂时返回None，表示功能未实现
            logger.warning("向量生成功能未实现")
            return None
            
        except Exception as e:
            logger.error(f"生成向量失败: {e}")
            return None

class SimpleVectorStore(VectorStore):
    """简单的内存向量存储（用于测试）"""
    
    def __init__(self):
        super().__init__()
        self.documents = {}
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档"""
        try:
            for doc in documents:
                doc_id = doc.get("id", f"doc_{len(self.documents)}")
                self.documents[doc_id] = doc
            
            logger.info(f"添加 {len(documents)} 个文档到内存存储")
            return True
            
        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            return False
    
    async def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """简单的关键词搜索"""
        try:
            results = []
            query_lower = query.lower()
            
            for doc_id, doc in self.documents.items():
                content = doc.get("content", "").lower()
                if query_lower in content:
                    result = doc.copy()
                    result["score"] = 1.0  # 简单的评分
                    results.append(result)
            
            # 限制结果数量
            results = results[:limit]
            
            logger.info(f"关键词搜索返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    async def delete_documents(self, document_ids: List[str]) -> bool:
        """删除文档"""
        try:
            for doc_id in document_ids:
                if doc_id in self.documents:
                    del self.documents[doc_id]
            
            logger.info(f"删除 {len(document_ids)} 个文档")
            return True
            
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False

def create_vector_store() -> VectorStore:
    """创建向量存储实例"""
    if settings.VECTOR_DB_URL and "qdrant" in settings.VECTOR_DB_URL.lower():
        return QdrantVectorStore()
    else:
        logger.info("使用简单内存向量存储")
        return SimpleVectorStore()
