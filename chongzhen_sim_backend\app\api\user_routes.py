"""
玩家相关API路由 - MongoDB版本
"""

from fastapi import APIRouter, HTTPException
from app.db.managers import player_manager
from app.schemas.player import PlayerCreate, PlayerResponse, PlayerUpdate
from app.utils.logger import setup_logger

logger = setup_logger(__name__)
router = APIRouter()

@router.post("/register", response_model=PlayerResponse)
async def register_player(player_data: PlayerCreate):
    """注册新玩家"""
    try:
        # 使用PlayerManager创建新玩家
        player = await player_manager.create_player(
            username=player_data.username,
            password=player_data.password,
            email=getattr(player_data, 'email', None)
        )

        logger.info(f"新玩家注册: {player_data.username}")
        return player
    except ValueError as e:
        # 用户名已存在等业务逻辑错误
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"玩家注册失败: {e}")
        raise HTTPException(status_code=500, detail="注册失败")

@router.post("/login")
async def login_player(username: str, password: str):
    """玩家登录"""
    try:
        # 使用PlayerManager进行认证
        player = await player_manager.authenticate_player(username, password)
        if not player:
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        # 这里可以生成JWT token
        logger.info(f"玩家登录: {username}")
        return {"message": "登录成功", "player_id": player.id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"玩家登录失败: {e}")
        raise HTTPException(status_code=500, detail="登录失败")

@router.get("/{player_id}", response_model=PlayerResponse)
async def get_player_info(player_id: str):
    """获取玩家信息"""
    try:
        # 使用PlayerManager获取玩家信息
        player = await player_manager.get_player_by_id(player_id)
        if not player:
            raise HTTPException(status_code=404, detail="玩家不存在")

        return player
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取玩家信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取玩家信息失败")

@router.put("/{player_id}", response_model=PlayerResponse)
async def update_player_info(player_id: str, player_update: PlayerUpdate):
    """更新玩家信息"""
    try:
        # 先获取玩家
        player = await player_manager.get_player_by_id(player_id)
        if not player:
            raise HTTPException(status_code=404, detail="玩家不存在")

        # 更新玩家属性
        update_data = player_update.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            if hasattr(player, key):
                setattr(player, key, value)

        # 保存更新
        success = await player_manager.update_player(player)
        if not success:
            raise HTTPException(status_code=500, detail="更新失败")

        return player
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新玩家信息失败: {e}")
        raise HTTPException(status_code=500, detail="更新玩家信息失败")

@router.get("/{player_id}/saves")
async def get_player_saves(player_id: str):
    """获取玩家存档列表"""
    try:
        # TODO: 实现存档查询逻辑
        return {"saves": []}
    except Exception as e:
        logger.error(f"获取存档列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取存档列表失败")

@router.post("/{player_id}/saves")
async def create_save(player_id: str, save_name: str):
    """创建新存档"""
    try:
        # TODO: 实现存档创建逻辑
        return {"message": "存档创建成功", "save_id": "1"}
    except Exception as e:
        logger.error(f"创建存档失败: {e}")
        raise HTTPException(status_code=500, detail="创建存档失败")
