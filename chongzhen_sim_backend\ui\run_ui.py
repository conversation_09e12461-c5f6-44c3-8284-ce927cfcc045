#!/usr/bin/env python3
"""
崇祯模拟器UI启动脚本
"""

import subprocess
import sys
import os

def install_requirements():
    """安装依赖"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    return True

def run_streamlit():
    """运行Streamlit应用"""
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0",
            "--theme.base", "light"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🚀 启动崇祯模拟器UI...")
    
    # 检查是否在正确的目录
    if not os.path.exists("main.py"):
        print("❌ 请在ui目录下运行此脚本")
        return
    
    # 安装依赖
    if not install_requirements():
        return
    
    # 运行应用
    print("🌐 启动Web界面...")
    print("📱 访问地址: http://localhost:8501")
    run_streamlit()

if __name__ == "__main__":
    main()
