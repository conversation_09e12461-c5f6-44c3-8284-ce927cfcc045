"""
回合相关数据结构
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any

class TurnResult(BaseModel):
    """回合结果"""
    success: bool
    title: str
    content: str
    data_changes: Dict[str, Any]
    next_turn_preview: str
    is_game_over: bool = False
    game_over_reason: Optional[str] = None

class TurnInput(BaseModel):
    """回合输入"""
    player_id: int
    edict_content: str
    current_turn: int
    current_year: int

class SimulationResult(BaseModel):
    """推演结果"""
    empire_changes: Dict[str, Any]
    events: List[Dict[str, Any]]
    narrative: str
    data_changes: Dict[str, Any]

class WorldEvent(BaseModel):
    """世界事件"""
    id: str
    type: str
    title: str
    description: str
    severity: int
    affected_regions: List[str]
    turn: int
    year: int

class DataChange(BaseModel):
    """数据变化"""
    resource_type: str
    old_value: float
    new_value: float
    change_amount: float
    change_reason: str

class TurnSummary(BaseModel):
    """回合摘要"""
    turn: int
    year: int
    edict_summary: str
    major_events: List[str]
    key_changes: List[DataChange]
    stability_status: str
