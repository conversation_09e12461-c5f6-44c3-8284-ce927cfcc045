"""
玩家数据访问层
"""

from typing import Optional, List
from bson import ObjectId
from datetime import datetime
from app.db.database import get_database
from app.db.models.player import Player
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class PlayerRepository:
    """玩家数据访问层"""
    
    def __init__(self):
        self.collection_name = "players"
    
    async def create_player(self, player: Player) -> Player:
        """创建玩家"""
        try:
            db = get_database()
            player_dict = player.model_dump(by_alias=True, exclude={"id"})
            result = await db[self.collection_name].insert_one(player_dict)
            player.id = result.inserted_id
            return player
        except Exception as e:
            logger.error(f"创建玩家失败: {e}")
            raise
    
    async def get_player_by_id(self, player_id: str) -> Optional[Player]:
        """根据ID获取玩家"""
        try:
            if not ObjectId.is_valid(player_id):
                return None
            
            db = get_database()
            player_data = await db[self.collection_name].find_one({"_id": ObjectId(player_id)})
            
            if player_data:
                return Player.from_dict(player_data)
            return None
        except Exception as e:
            logger.error(f"获取玩家失败: {e}")
            raise
    
    async def get_player_by_username(self, username: str) -> Optional[Player]:
        """根据用户名获取玩家"""
        try:
            db = get_database()
            player_data = await db[self.collection_name].find_one({"username": username})
            
            if player_data:
                return Player.from_dict(player_data)
            return None
        except Exception as e:
            logger.error(f"根据用户名获取玩家失败: {e}")
            raise
    
    async def update_player(self, player_id: str, update_data: dict) -> bool:
        """更新玩家信息"""
        try:
            if not ObjectId.is_valid(player_id):
                return False
            
            db = get_database()
            update_data["updated_at"] = datetime.utcnow()
            
            result = await db[self.collection_name].update_one(
                {"_id": ObjectId(player_id)},
                {"$set": update_data}
            )
            
            return result.matched_count > 0
        except Exception as e:
            logger.error(f"更新玩家失败: {e}")
            raise
    
    async def delete_player(self, player_id: str) -> bool:
        """删除玩家"""
        try:
            if not ObjectId.is_valid(player_id):
                return False
            
            db = get_database()
            result = await db[self.collection_name].delete_one({"_id": ObjectId(player_id)})
            
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除玩家失败: {e}")
            raise
    
    async def list_players(self, skip: int = 0, limit: int = 100) -> List[Player]:
        """获取玩家列表"""
        try:
            db = get_database()
            cursor = db[self.collection_name].find().skip(skip).limit(limit)
            
            players = []
            async for player_data in cursor:
                players.append(Player.from_dict(player_data))
            
            return players
        except Exception as e:
            logger.error(f"获取玩家列表失败: {e}")
            raise
    
    async def update_last_active(self, player_id: str) -> bool:
        """更新最后活跃时间"""
        try:
            return await self.update_player(player_id, {"last_active": datetime.utcnow()})
        except Exception as e:
            logger.error(f"更新最后活跃时间失败: {e}")
            raise

# 全局实例
player_repository = PlayerRepository()
