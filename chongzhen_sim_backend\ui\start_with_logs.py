#!/usr/bin/env python3
"""
启动崇祯模拟器UI和日志查看器
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def start_main_app():
    """启动主应用"""
    print("🚀 启动主应用...")
    process = subprocess.Popen([
        sys.executable, "-m", "streamlit", "run", "main.py",
        "--server.port", "8501",
        "--server.address", "127.0.0.1"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    return process

def start_log_viewer():
    """启动日志查看器"""
    print("📊 启动日志查看器...")
    process = subprocess.Popen([
        sys.executable, "-m", "streamlit", "run", "log_viewer.py",
        "--server.port", "8502",
        "--server.address", "127.0.0.1"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    return process

def main():
    """主函数"""
    print("👑 崇祯模拟器 - 完整启动")
    print("=" * 50)
    
    # 启动主应用
    main_process = start_main_app()
    time.sleep(3)
    
    # 启动日志查看器
    log_process = start_log_viewer()
    time.sleep(3)
    
    print("\n" + "=" * 50)
    print("🎉 启动完成！")
    print("🎮 主应用: http://127.0.0.1:8501")
    print("📊 日志查看器: http://127.0.0.1:8502")
    print("=" * 50)
    print("按 Ctrl+C 停止所有服务")
    
    # 自动打开浏览器
    try:
        webbrowser.open("http://127.0.0.1:8501")
        time.sleep(2)
        webbrowser.open("http://127.0.0.1:8502")
    except:
        pass
    
    try:
        # 等待用户中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        
        if main_process:
            main_process.terminate()
            print("✅ 主应用已停止")
        
        if log_process:
            log_process.terminate()
            print("✅ 日志查看器已停止")
        
        print("👋 所有服务已关闭")

if __name__ == "__main__":
    main()
