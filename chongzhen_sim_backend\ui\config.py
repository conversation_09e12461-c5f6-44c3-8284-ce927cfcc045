"""
UI配置文件
"""

import os

# API配置
API_BASE_URL = os.getenv("API_BASE_URL", "http://127.0.0.1:8000")

# UI配置
UI_TITLE = "崇祯模拟器"
UI_ICON = "👑"

# 页面配置
PAGES = {
    "主界面": "🏠",
    "个人数据": "👤", 
    "势力数据": "🏛️",
    "地图": "🗺️",
    "召见大臣": "👥",
    "诏书写作": "📜"
}

# 颜色主题
COLORS = {
    "primary": "#8B4513",
    "secondary": "#CD853F", 
    "success": "#28a745",
    "warning": "#ffc107",
    "danger": "#dc3545",
    "info": "#17a2b8"
}

# 默认数据
DEFAULT_REGIONS = [
    {
        "name": "北京",
        "population": 1200000,
        "stability": 0.8,
        "prosperity": 0.8,
        "loyalty": 0.9,
        "type": "京师",
        "description": "大明帝国的政治中心，皇城所在地"
    },
    {
        "name": "南京",
        "population": 800000,
        "stability": 0.7,
        "prosperity": 0.8,
        "loyalty": 0.8,
        "type": "留都",
        "description": "明朝的陪都，江南重镇"
    },
    {
        "name": "辽东",
        "population": 300000,
        "stability": 0.4,
        "prosperity": 0.3,
        "loyalty": 0.6,
        "type": "边关",
        "description": "边防重地，与后金接壤"
    },
    {
        "name": "山西",
        "population": 2000000,
        "stability": 0.6,
        "prosperity": 0.6,
        "loyalty": 0.7,
        "type": "省份",
        "description": "煤炭丰富，商人众多"
    },
    {
        "name": "陕西",
        "population": 1800000,
        "stability": 0.3,
        "prosperity": 0.4,
        "loyalty": 0.5,
        "type": "省份",
        "description": "民风彪悍，易生叛乱"
    },
    {
        "name": "江南",
        "population": 3000000,
        "stability": 0.8,
        "prosperity": 0.9,
        "loyalty": 0.8,
        "type": "地区",
        "description": "鱼米之乡，赋税重地"
    }
]

DEFAULT_MINISTERS = [
    {
        "name": "温体仁",
        "position": "内阁首辅",
        "loyalty": 0.7,
        "ability": 0.6,
        "faction": "保守派",
        "description": "谨慎保守，善于察言观色"
    },
    {
        "name": "袁崇焕",
        "position": "兵部尚书",
        "loyalty": 0.9,
        "ability": 0.9,
        "faction": "主战派",
        "description": "刚直不阿，军事才能出众"
    },
    {
        "name": "魏忠贤",
        "position": "司礼监太监",
        "loyalty": 0.3,
        "ability": 0.8,
        "faction": "宦官集团",
        "description": "狡诈多疑，权欲极强"
    },
    {
        "name": "孙承宗",
        "position": "辽东总督",
        "loyalty": 0.8,
        "ability": 0.8,
        "faction": "务实派",
        "description": "老成持重，军事经验丰富"
    }
]

# 诏书模板
EDICT_TEMPLATES = {
    "空白诏书": "",
    "赈灾诏书": "朕闻民有饥馑，心甚不安。特下诏开仓赈济，务使百姓得以安生。各地官员当体朕心，速速办理，不得有误。",
    "军事诏书": "边关告急，朕心忧虑。特下诏调兵遣将，务必保卫疆土。将士当奋勇杀敌，为国捐躯，朕必厚赏。",
    "税收诏书": "国库空虚，百废待兴。特下诏调整赋税，以充实国库。然不可过重，免致民怨。各地官员当公正征收。",
    "人事诏书": "朝廷用人，当以德才为先。特下诏任免官员，以振朝纲。贤者当用，庸者当退，不得徇私。",
    "改革诏书": "时势变迁，当与时俱进。特下诏推行新政，以图国家富强。虽有阻力，朕意已决，众卿当协力办理。"
}

# AI建议
AI_SUGGESTIONS = {
    "政治建议": [
        "建议加强对地方官员的监督，设立巡察制度，防止贪腐。",
        "建议重用贤能之士，广开言路，听取民意。",
        "建议整顿朝纲，严惩贪腐官员，重振朝廷威信。",
        "建议改革科举制度，选拔真正的人才。",
        "建议加强中央集权，统一政令。"
    ],
    "经济建议": [
        "建议减免部分地区赋税，减轻民众负担，恢复生产。",
        "建议开放商贸，促进经济发展，增加税收。",
        "建议兴修水利，发展农业生产，保障粮食安全。",
        "建议发展手工业，提高产品质量，增强竞争力。",
        "建议整顿货币制度，稳定物价。"
    ],
    "军事建议": [
        "建议加强边防建设，防范外敌入侵。",
        "建议整顿军纪，提高军队战斗力。",
        "建议增加军饷，稳定军心，防止哗变。",
        "建议改进武器装备，提升军队实力。",
        "建议加强军事训练，提高士兵素质。"
    ],
    "民生建议": [
        "建议开仓赈济，救助灾民，稳定民心。",
        "建议兴办学校，教育民众，提高文化水平。",
        "建议修建道路，便民出行，促进商贸。",
        "建议改善医疗条件，防治疾病。",
        "建议关注民生疾苦，体恤百姓。"
    ],
    "外交建议": [
        "建议派遣使者，加强与邻国关系。",
        "建议以和为贵，避免不必要的战争。",
        "建议加强边境贸易，促进交流。",
        "建议联合盟友，共同对抗敌国。",
        "建议通过外交手段解决争端。"
    ]
}
