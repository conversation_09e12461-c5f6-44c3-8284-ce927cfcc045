"""
大臣行动Agent - 处理大臣在回合中的自主行动
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.agents.base_agent import BaseAgent, AgentInput, AgentOutput
# 临时定义MinisterActionResult
class MinisterActionResult:
    def __init__(self, minister_id: str, minister_name: str, success: bool,
                 memory_updates=None, dialogue_strategy_updates=None,
                 external_actions=None, resource_changes=None,
                 execution_time_seconds=0, error_message=""):
        self.minister_id = minister_id
        self.minister_name = minister_name
        self.success = success
        self.memory_updates = memory_updates or []
        self.dialogue_strategy_updates = dialogue_strategy_updates or {}
        self.external_actions = external_actions or []
        self.resource_changes = resource_changes or {}
        self.execution_time_seconds = execution_time_seconds
        self.error_message = error_message
from app.db.managers import character_manager
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class MinisterActionInput(AgentInput):
    """大臣行动输入"""
    minister_id: str
    minister_data: Dict[str, Any]
    current_empire_state: Dict[str, Any]
    recent_events: List[Dict[str, Any]] = []
    emperor_recent_actions: List[str] = []
    current_turn: int
    current_year: int

class MinisterActionOutput(BaseModel):
    """大臣行动输出"""
    minister_id: str
    minister_name: str
    
    # 自身修改
    memory_updates: List[str] = []
    dialogue_strategy: Dict[str, Any] = {}
    
    # 对外行动
    external_actions: List[Dict[str, Any]] = []
    resource_allocations: Dict[str, float] = {}
    
    # 推理过程
    reasoning: str = ""

class MinisterActionAgent(BaseAgent):
    """大臣行动Agent"""
    
    def get_system_prompt(self) -> str:
        return """你是明朝大臣行动模拟系统，负责模拟大臣在每个回合中的自主行动。

你的职责：
1. 分析大臣的当前状态、忠诚度、能力和政治立场
2. 基于当前帝国状况和近期事件，决定大臣的行动策略
3. 模拟大臣的自身调整（记忆更新、对话策略等）
4. 模拟大臣的对外行动（资源调配、政策执行等）

大臣行动包含两个方面：

A. 自身修改：
- 更新记忆：基于近期事件和皇帝行为，更新对当前局势的认知
- 调整对话策略：决定下次与皇帝对话时的态度、重点和策略
- 个人发展：提升能力、扩展人脉、积累资源等

B. 对外行动：
- 行政行动：在职责范围内的政策执行、资源调配
- 军事行动：如果有军事职责，进行兵力调动、防务安排
- 经济行动：商贸政策、税收调整、基础设施建设
- 外交行动：与其他势力的交涉、情报收集
- 派系活动：与同派系成员的协调、对敌对派系的制衡

输出要求：
- 所有行动必须符合大臣的身份、能力和权限
- 考虑大臣的忠诚度、个人利益和政治立场
- 行动要有明确的目标和预期效果
- 提供详细的推理过程

输出格式为JSON：
{
    "minister_id": "大臣ID",
    "minister_name": "大臣姓名",
    "memory_updates": ["记忆更新1", "记忆更新2"],
    "dialogue_strategy": {
        "attitude": "恭敬/谨慎/积极/消极",
        "key_topics": ["要讨论的重点话题"],
        "hidden_agenda": "隐藏的个人目标",
        "loyalty_change": 0.1  // 忠诚度变化
    },
    "external_actions": [
        {
            "type": "administrative/military/economic/diplomatic",
            "description": "行动描述",
            "target": "行动目标",
            "expected_effect": "预期效果",
            "resource_cost": {"gold": 1000, "manpower": 100}
        }
    ],
    "resource_allocations": {
        "region_name": 1000.0  // 向特定地区分配的资源
    },
    "reasoning": "详细的推理过程和决策依据"
}"""

    async def process(self, input_data: MinisterActionInput) -> AgentOutput:
        """处理大臣行动"""
        try:
            logger.info(f"开始处理大臣行动，大臣ID: {input_data.minister_id}")
            
            # 构建提示词
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": self._build_action_prompt(input_data)}
            ]
            
            # 调用LLM
            response = await self.call_llm(messages, temperature=0.8, max_tokens=2000)
            
            # 解析响应
            try:
                action_data = json.loads(response)
                result = MinisterActionOutput(**action_data)
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"解析大臣行动结果失败: {e}")
                # 返回默认结果
                result = MinisterActionOutput(
                    minister_id=input_data.minister_id,
                    minister_name=input_data.minister_data.get("name", "未知大臣"),
                    reasoning="解析失败，使用默认行动"
                )
            
            # 更新数据库中的大臣信息
            await self._update_minister_data(input_data.minister_id, result)
            
            logger.info(f"大臣行动处理完成，大臣: {result.minister_name}")
            
            return AgentOutput(
                success=True,
                result=result,
                metadata={
                    "execution_time": 0,  # TODO: 计算实际执行时间
                    "minister_name": result.minister_name
                }
            )
            
        except Exception as e:
            logger.error(f"大臣行动处理失败: {e}")
            return AgentOutput(
                success=False,
                result=None,
                error_message=str(e)
            )
    
    def _build_action_prompt(self, input_data: MinisterActionInput) -> str:
        """构建行动提示词"""
        minister = input_data.minister_data
        empire_state = input_data.current_empire_state
        
        prompt = f"""
请为以下大臣制定本回合的行动计划：

=== 大臣信息 ===
姓名：{minister.get('name', '未知')}
职位：{minister.get('title', '未知')}
年龄：{minister.get('age', 40)}
忠诚度：{minister.get('game_stats', {}).get('loyalty', 0.5):.2f}
行政能力：{minister.get('game_stats', {}).get('administrative_ability', 0.5):.2f}
军事能力：{minister.get('game_stats', {}).get('military_ability', 0.5):.2f}
经济能力：{minister.get('game_stats', {}).get('economic_ability', 0.5):.2f}
外交能力：{minister.get('game_stats', {}).get('diplomatic_ability', 0.5):.2f}
派系：{minister.get('game_stats', {}).get('faction', '无')}
政治立场：{minister.get('game_stats', {}).get('political_stance', '中立')}
个人财富：{minister.get('game_stats', {}).get('personal_wealth', 10000)}
当前位置：{minister.get('game_stats', {}).get('current_location', '京师')}

=== 当前帝国状况 ===
国库：{empire_state.get('treasury', 0)}两银子
粮食储备：{empire_state.get('food_storage', 0)}石
军队数量：{empire_state.get('total_military', 0)}人
人口：{empire_state.get('population', 0)}人
内政稳定度：{empire_state.get('internal_stability', 0.5):.2f}
军心士气：{empire_state.get('military_morale', 0.5):.2f}

=== 近期事件 ===
{self._format_recent_events(input_data.recent_events)}

=== 皇帝近期行动 ===
{self._format_emperor_actions(input_data.emperor_recent_actions)}

=== 当前时间 ===
崇祯{input_data.current_year}年，第{input_data.current_turn}回合

请基于以上信息，制定该大臣本回合的具体行动计划。
"""
        return prompt
    
    def _format_recent_events(self, events: List[Dict[str, Any]]) -> str:
        """格式化近期事件"""
        if not events:
            return "暂无重大事件"
        
        formatted = []
        for event in events[-5:]:  # 只显示最近5个事件
            formatted.append(f"- {event.get('title', '未知事件')}: {event.get('description', '无描述')}")
        
        return "\n".join(formatted)
    
    def _format_emperor_actions(self, actions: List[str]) -> str:
        """格式化皇帝近期行动"""
        if not actions:
            return "皇帝近期无特殊行动"
        
        return "\n".join([f"- {action}" for action in actions[-3:]])  # 只显示最近3个行动
    
    async def _update_minister_data(self, minister_id: str, result: MinisterActionOutput):
        """更新大臣数据"""
        try:
            # 更新大臣的记忆和对话策略
            update_data = {
                "interaction_history": {
                    "memory_updates": result.memory_updates,
                    "dialogue_strategy": result.dialogue_strategy,
                    "last_action_turn": 0  # TODO: 获取当前回合数
                }
            }
            
            # 如果有忠诚度变化，更新忠诚度
            if result.dialogue_strategy.get("loyalty_change"):
                loyalty_change = result.dialogue_strategy["loyalty_change"]
                # TODO: 实现忠诚度更新逻辑
                logger.info(f"大臣 {minister_id} 忠诚度变化: {loyalty_change}")
            
            # TODO: 调用character_manager更新数据
            logger.info(f"更新大臣数据: {minister_id}")
            
        except Exception as e:
            logger.error(f"更新大臣数据失败: {e}")

    async def process_multiple_ministers(
        self, 
        ministers_data: List[Dict[str, Any]], 
        empire_state: Dict[str, Any],
        recent_events: List[Dict[str, Any]],
        emperor_recent_actions: List[str],
        current_turn: int,
        current_year: int
    ) -> List[MinisterActionResult]:
        """并行处理多个大臣的行动"""
        try:
            logger.info(f"开始并行处理 {len(ministers_data)} 个大臣的行动")
            
            # 创建并发任务
            tasks = []
            for minister in ministers_data:
                input_data = MinisterActionInput(
                    player_id="",  # TODO: 传入正确的player_id
                    minister_id=minister.get("id", ""),
                    minister_data=minister,
                    current_empire_state=empire_state,
                    recent_events=recent_events,
                    emperor_recent_actions=emperor_recent_actions,
                    current_turn=current_turn,
                    current_year=current_year
                )
                tasks.append(self.process(input_data))
            
            # 并发执行
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            minister_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"大臣 {ministers_data[i].get('name', '未知')} 行动失败: {result}")
                    # 创建失败结果
                    minister_results.append(MinisterActionResult(
                        minister_id=ministers_data[i].get("id", ""),
                        minister_name=ministers_data[i].get("name", "未知大臣"),
                        success=False,
                        error_message=str(result),
                        execution_time_seconds=0
                    ))
                elif result.success:
                    # 转换为MinisterActionResult
                    output = result.result
                    minister_results.append(MinisterActionResult(
                        minister_id=output.minister_id,
                        minister_name=output.minister_name,
                        success=True,
                        memory_updates=output.memory_updates,
                        dialogue_strategy_updates=output.dialogue_strategy,
                        external_actions=output.external_actions,
                        resource_changes=output.resource_allocations,
                        execution_time_seconds=result.metadata.get("execution_time", 0)
                    ))
                else:
                    # 处理失败的结果
                    minister_results.append(MinisterActionResult(
                        minister_id=ministers_data[i].get("id", ""),
                        minister_name=ministers_data[i].get("name", "未知大臣"),
                        success=False,
                        error_message=result.error_message or "未知错误",
                        execution_time_seconds=0
                    ))
            
            logger.info(f"大臣行动并行处理完成，成功: {sum(1 for r in minister_results if r.success)}/{len(minister_results)}")
            return minister_results
            
        except Exception as e:
            logger.error(f"并行处理大臣行动失败: {e}")
            return []
