#!/usr/bin/env python3
"""
测试前端UI的LLM对话功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ui'))

# 先导入必要的模块
import requests
import time

# 模拟streamlit session_state
class MockSessionState:
    def __init__(self):
        self.player_id = None

# 创建模拟的streamlit模块
class MockStreamlit:
    def __init__(self):
        self.session_state = MockSessionState()

# 将模拟的streamlit添加到sys.modules
sys.modules['streamlit'] = MockStreamlit()
import streamlit as st

# 现在导入UI模块
from ui.main import generate_minister_reply, get_npc_id_mapping

def test_npc_mapping():
    """测试NPC ID映射功能"""
    print("=== 测试NPC ID映射 ===")
    
    mapping = get_npc_id_mapping()
    print(f"获取到的NPC映射: {mapping}")
    
    if mapping:
        print("✅ NPC映射获取成功")
        for name, npc_id in mapping.items():
            print(f"  - {name}: {npc_id}")
    else:
        print("❌ NPC映射获取失败")
    
    return mapping

def test_minister_reply():
    """测试大臣回复功能"""
    print("\n=== 测试大臣回复功能 ===")
    
    # 模拟大臣数据
    test_ministers = [
        {"name": "温体仁", "position": "内阁首辅"},
        {"name": "袁崇焕", "position": "兵部尚书"},
        {"name": "魏忠贤", "position": "司礼监太监"}
    ]
    
    # 模拟用户输入
    test_messages = [
        "爱卿，近日朝政如何？",
        "边防告急，你有何良策？",
        "国库空虚，有何良策？"
    ]
    
    # 首先需要注册一个测试用户
    print("注册测试用户...")
    import time
    username = f"ui_test_{int(time.time())}"
    
    response = requests.post("http://127.0.0.1:8000/api/user/register", json={
        "username": username,
        "password": "test123",
        "game_difficulty": "normal"
    })
    
    if response.status_code == 200:
        player_data = response.json()
        player_id = player_data.get("id")
        print(f"✅ 用户注册成功，ID: {player_id}")
        
        # 设置session state
        st.session_state.player_id = player_id
        
        # 测试每个大臣的回复
        for minister in test_ministers:
            print(f"\n--- 测试与{minister['name']}的对话 ---")
            
            for message in test_messages:
                print(f"\n皇帝: {message}")
                
                try:
                    reply = generate_minister_reply(minister, message)
                    print(f"{minister['name']}: {reply}")
                    print("✅ 对话成功")
                except Exception as e:
                    print(f"❌ 对话失败: {str(e)}")
                
                # 稍微延迟
                time.sleep(1)
    else:
        print(f"❌ 用户注册失败: {response.status_code}")

def main():
    """主测试函数"""
    print("🧪 开始测试前端UI的LLM对话功能")
    
    # 测试NPC映射
    mapping = test_npc_mapping()
    
    if mapping:
        # 测试大臣回复
        test_minister_reply()
    else:
        print("❌ 由于NPC映射失败，跳过大臣回复测试")
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
