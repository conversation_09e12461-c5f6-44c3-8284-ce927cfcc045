"""
大臣NPC数据模型 - MongoDB版本
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
import json

class Character(BaseModel):
    """大臣NPC模型"""
    id: Optional[str] = Field(default=None, alias="_id")
    name: str = Field(..., description="角色名称")
    title: str = Field(..., description="职位/称号")

    # 基本属性
    age: int = Field(default=40, description="年龄")
    health: float = Field(default=0.8, description="健康状况 0-1")

    # 游戏机制属性
    game_stats: Dict[str, Any] = Field(default_factory=lambda: {
        "loyalty": 0.6,                    # 忠诚度
        "administrative_ability": 0.5,     # 行政能力
        "military_ability": 0.5,           # 军事能力
        "diplomatic_ability": 0.5,         # 外交能力
        "economic_ability": 0.5,           # 经济能力
        "faction": "",                     # 所属派系
        "political_stance": "",            # 政治立场
        "personal_interests": "",          # 个人利益
        "allies": [],                      # 盟友列表
        "enemies": [],                     # 敌人列表
        "personal_wealth": 10000.0,        # 个人财富
        "land_holdings": 0,                # 土地持有
        "private_army": 0,                 # 私人武装
        "major_achievements": "",          # 主要成就
        "major_failures": "",              # 主要失败
        "current_location": "京师"         # 当前位置
    }, description="游戏机制相关属性")

    # 对话系统属性
    dialogue_profile: Dict[str, Any] = Field(default_factory=lambda: {
        "personality": "",                 # 性格特点
        "background": "",                  # 背景故事
        "speaking_style": "",              # 说话风格
        "system_prompt": "",               # 系统提示词
        "relationship_with_emperor": ""   # 与皇帝的关系
    }, description="对话系统相关属性")

    # 互动历史
    interaction_history: List[Dict[str, Any]] = Field(default_factory=list, description="互动历史记录")

    # 新增：行为历史记录
    action_history: Dict[int, str] = Field(default_factory=dict, description="每一轮(int)这个大臣进行的行为(str)")

    # 状态信息
    is_active: bool = Field(default=True, description="是否激活")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True

    def __init__(self, **data):
        """初始化大臣对象"""
        super().__init__(**data)

    # 游戏机制相关方法
    def get_loyalty(self) -> float:
        """获取忠诚度"""
        return self.game_stats.get("loyalty", 0.6)

    def modify_loyalty(self, change: float):
        """修改忠诚度"""
        current_loyalty = self.get_loyalty()
        new_loyalty = max(0.0, min(1.0, current_loyalty + change))
        self.game_stats["loyalty"] = new_loyalty
        self.updated_at = datetime.utcnow()

    def get_ability(self, ability_type: str) -> float:
        """获取特定能力值"""
        ability_map = {
            "administrative": "administrative_ability",
            "military": "military_ability", 
            "diplomatic": "diplomatic_ability",
            "economic": "economic_ability"
        }
        key = ability_map.get(ability_type, f"{ability_type}_ability")
        return self.game_stats.get(key, 0.5)

    def modify_ability(self, ability_type: str, change: float):
        """修改能力值"""
        ability_map = {
            "administrative": "administrative_ability",
            "military": "military_ability",
            "diplomatic": "diplomatic_ability", 
            "economic": "economic_ability"
        }
        key = ability_map.get(ability_type, f"{ability_type}_ability")
        current_value = self.game_stats.get(key, 0.5)
        new_value = max(0.0, min(1.0, current_value + change))
        self.game_stats[key] = new_value
        self.updated_at = datetime.utcnow()

    def modify_health(self, change: float):
        """修改健康状况"""
        self.health = max(0.0, min(1.0, self.health + change))
        self.updated_at = datetime.utcnow()

    def add_ally(self, ally_name: str):
        """添加盟友"""
        if ally_name not in self.game_stats.get("allies", []):
            self.game_stats.setdefault("allies", []).append(ally_name)
            self.updated_at = datetime.utcnow()

    def remove_ally(self, ally_name: str):
        """移除盟友"""
        allies = self.game_stats.get("allies", [])
        if ally_name in allies:
            allies.remove(ally_name)
            self.updated_at = datetime.utcnow()

    def add_enemy(self, enemy_name: str):
        """添加敌人"""
        if enemy_name not in self.game_stats.get("enemies", []):
            self.game_stats.setdefault("enemies", []).append(enemy_name)
            self.updated_at = datetime.utcnow()

    def remove_enemy(self, enemy_name: str):
        """移除敌人"""
        enemies = self.game_stats.get("enemies", [])
        if enemy_name in enemies:
            enemies.remove(enemy_name)
            self.updated_at = datetime.utcnow()

    # 行为历史相关方法
    def add_action(self, turn: int, action: str):
        """添加某一轮的行为记录"""
        self.action_history[turn] = action
        self.updated_at = datetime.utcnow()

    def get_action(self, turn: int) -> Optional[str]:
        """获取某一轮的行为记录"""
        return self.action_history.get(turn)

    def get_recent_actions(self, num_turns: int = 5) -> Dict[int, str]:
        """获取最近几轮的行为记录"""
        if not self.action_history:
            return {}
        
        sorted_turns = sorted(self.action_history.keys(), reverse=True)
        recent_turns = sorted_turns[:num_turns]
        return {turn: self.action_history[turn] for turn in recent_turns}

    def clear_action_history(self):
        """清空行为历史"""
        self.action_history = {}
        self.updated_at = datetime.utcnow()

    # 互动历史相关方法
    def add_interaction(self, interaction_data: Dict[str, Any]):
        """添加互动记录"""
        interaction_data["timestamp"] = datetime.utcnow().isoformat()
        self.interaction_history.append(interaction_data)
        self.updated_at = datetime.utcnow()

    def get_recent_interactions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的互动记录"""
        return self.interaction_history[-limit:] if self.interaction_history else []

    # 状态检查方法
    def is_healthy(self) -> bool:
        """检查是否健康"""
        return self.health > 0.3

    def is_loyal(self) -> bool:
        """检查是否忠诚"""
        return self.get_loyalty() > 0.5

    def get_overall_capability(self) -> float:
        """获取综合能力评分"""
        abilities = [
            self.get_ability("administrative"),
            self.get_ability("military"),
            self.get_ability("diplomatic"),
            self.get_ability("economic")
        ]
        return sum(abilities) / len(abilities)

    # 数据转换方法
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "_id": str(self.id) if self.id else None,
            "name": self.name,
            "title": self.title,
            "age": self.age,
            "health": self.health,
            "game_stats": self.game_stats,
            "dialogue_profile": self.dialogue_profile,
            "interaction_history": self.interaction_history,
            "action_history": self.action_history,
            "is_active": self.is_active,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Character':
        """从字典创建实例"""
        if "_id" in data:
            data["id"] = str(data.pop("_id"))
        return cls(**data)

    def __repr__(self):
        return f"<Character(id={self.id}, name='{self.name}', title='{self.title}', loyalty={self.get_loyalty():.2f})>"
