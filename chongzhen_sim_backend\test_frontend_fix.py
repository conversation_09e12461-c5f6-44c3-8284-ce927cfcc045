#!/usr/bin/env python3
"""
测试前端修复后的LLM对话功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟streamlit session_state
class MockSessionState:
    def __init__(self):
        self.player_id = None

# 创建模拟的streamlit模块
class MockStreamlit:
    def __init__(self):
        self.session_state = MockSessionState()

# 将模拟的streamlit添加到sys.modules
sys.modules['streamlit'] = MockStreamlit()
import streamlit as st

# 现在导入UI模块的函数
from ui.main import generate_minister_reply, get_npc_id_mapping
import requests
import time

def test_frontend_fix():
    """测试前端修复"""
    print("🧪 测试前端修复后的LLM对话功能")
    print("=" * 60)
    
    # 1. 注册测试用户
    print("\n1️⃣ 注册测试用户...")
    username = f"frontend_fix_test_{int(time.time())}"
    response = requests.post("http://127.0.0.1:8000/api/user/register", json={
        "username": username,
        "password": "test123",
        "game_difficulty": "normal"
    })
    
    if response.status_code == 200:
        player_data = response.json()
        player_id = player_data.get("id")
        print(f"✅ 用户注册成功，player_id: {player_id}")
        
        # 设置session state
        st.session_state.player_id = player_id
        
        # 2. 测试大臣对话
        print("\n2️⃣ 测试大臣对话...")
        
        # 模拟前端的大臣数据
        test_minister = {"name": "温体仁", "position": "内阁首辅"}
        test_message = "爱卿，近日朝政如何？"
        
        print(f"\n--- 测试与{test_minister['name']}的对话 ---")
        print(f"👑 皇帝: {test_message}")
        
        # 调用前端的generate_minister_reply函数
        reply = generate_minister_reply(test_minister, test_message)
        
        print(f"🎭 {test_minister['name']}回复: {reply}")
        
        if "臣遵旨" in reply and len(reply) < 20:
            print("⚠️ 返回的是简单模拟回复，LLM对话可能失败")
        else:
            print("✅ 返回的是LLM智能回复")
        
        print("\n🏁 前端修复测试完成！")
        
    else:
        print(f"❌ 用户注册失败: {response.status_code}")

if __name__ == "__main__":
    test_frontend_fix()
