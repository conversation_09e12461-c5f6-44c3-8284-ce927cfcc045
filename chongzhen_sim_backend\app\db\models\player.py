"""
玩家数据模型 - MongoDB版本
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
import hashlib
import json

class Player(BaseModel):
    """玩家模型"""
    id: Optional[str] = Field(default=None, alias="_id")
    username: str = Field(..., min_length=3, max_length=50)
    password_hash: str = ""
    email: Optional[str] = None

    # 游戏进度
    current_turn: int = 1
    current_year: int = 1  # 崇祯年号
    game_started: datetime = Field(default_factory=datetime.utcnow)
    last_active: datetime = Field(default_factory=datetime.utcnow)
    edicts_history: Dict[int, str] # 记录每一回合的诏书内容

    # 崇祯个人属性
    emperor_age: int = 17  # 崇祯登基时17岁
    emperor_health: float = 0.8
    emperor_insight: float = 0.6  # 洞察力
    emperor_charisma: float = 0.5  # 魅力
    emperor_intelligence: float = 0.7  # 智力

    # 游戏状态
    is_game_active: bool = True
    game_difficulty: str = "normal"  # easy, normal, hard

    # 统计信息
    total_edicts_issued: int = 0
    total_ministers_summoned: int = 0
    total_crises_resolved: int = 0

    # 成就和记录
    achievements: str = "{}"  # JSON格式存储成就
    game_notes: str = ""  # 玩家笔记

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True

    def __init__(self, **data):
        """初始化玩家对象"""
        super().__init__(**data)
        # 如果没有设置密码哈希，初始化为空字符串
        if not self.password_hash:
            self.password_hash = ""
    
    def set_password(self, password: str):
        """设置密码"""
        self.password_hash = hashlib.sha256(password.encode()).hexdigest()

    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return self.password_hash == hashlib.sha256(password.encode()).hexdigest()

    def get_game_progress(self) -> dict:
        """获取游戏进度信息"""
        return {
            "current_turn": self.current_turn,
            "current_year": self.current_year,
            "years_passed": self.current_year - 1,
            "total_turns": self.current_turn,
            "game_duration_days": (self.last_active - self.game_started).days if self.last_active and self.game_started else 0
        }

    def get_emperor_attributes(self) -> dict:
        """获取崇祯个人属性"""
        return {
            "age": self.emperor_age,
            "health": self.emperor_health,
            "insight": self.emperor_insight,
            "charisma": self.emperor_charisma,
            "intelligence": self.emperor_intelligence
        }

    def update_emperor_age(self):
        """更新崇祯年龄（每年调用）"""
        self.emperor_age = 17 + (self.current_year - 1)

    def modify_emperor_attribute(self, attribute: str, change: float):
        """修改崇祯属性"""
        if attribute == "health":
            self.emperor_health = max(0.0, min(1.0, self.emperor_health + change))
        elif attribute == "insight":
            self.emperor_insight = max(0.0, min(1.0, self.emperor_insight + change))
        elif attribute == "charisma":
            self.emperor_charisma = max(0.0, min(1.0, self.emperor_charisma + change))
        elif attribute == "intelligence":
            self.emperor_intelligence = max(0.0, min(1.0, self.emperor_intelligence + change))
    
    def add_achievement(self, achievement_id: str, achievement_data: dict):
        """添加成就"""
        achievements = json.loads(self.achievements) if self.achievements else {}
        achievements[achievement_id] = {
            "data": achievement_data,
            "unlocked_at": datetime.utcnow().isoformat()
        }
        self.achievements = json.dumps(achievements)

    def get_achievements(self) -> dict:
        """获取成就列表"""
        return json.loads(self.achievements) if self.achievements else {}

    def increment_stat(self, stat_name: str):
        """增加统计数据"""
        if stat_name == "edicts":
            self.total_edicts_issued += 1
        elif stat_name == "summons":
            self.total_ministers_summoned += 1
        elif stat_name == "crises":
            self.total_crises_resolved += 1

    def get_statistics(self) -> dict:
        """获取统计信息"""
        return {
            "total_edicts_issued": self.total_edicts_issued,
            "total_ministers_summoned": self.total_ministers_summoned,
            "total_crises_resolved": self.total_crises_resolved,
            "average_edicts_per_year": self.total_edicts_issued / max(1, self.current_year - 1) if self.current_year > 1 else 0
        }

    def is_game_over(self) -> bool:
        """检查游戏是否结束"""
        # 健康状况过差
        if self.emperor_health <= 0.1:
            return True

        # 年龄过大（历史上崇祯34岁自缢）
        if self.emperor_age >= 50:
            return True

        # 游戏不活跃
        if not self.is_game_active:
            return True

        return False

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "_id": str(self.id),
            "username": self.username,
            "password_hash": self.password_hash,
            "email": self.email,
            "current_turn": self.current_turn,
            "current_year": self.current_year,
            "game_started": self.game_started,
            "last_active": self.last_active,
            "emperor_age": self.emperor_age,
            "emperor_health": self.emperor_health,
            "emperor_insight": self.emperor_insight,
            "emperor_charisma": self.emperor_charisma,
            "emperor_intelligence": self.emperor_intelligence,
            "is_game_active": self.is_game_active,
            "game_difficulty": self.game_difficulty,
            "total_edicts_issued": self.total_edicts_issued,
            "total_ministers_summoned": self.total_ministers_summoned,
            "total_crises_resolved": self.total_crises_resolved,
            "achievements": self.achievements,
            "game_notes": self.game_notes,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Player':
        """从字典创建实例"""
        if "_id" in data:
            data["id"] = str(data.pop("_id"))
        return cls(**data)

    def __repr__(self):
        return f"<Player(id={self.id}, username='{self.username}', year={self.current_year}, turn={self.current_turn})>"
