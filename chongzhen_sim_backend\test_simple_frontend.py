#!/usr/bin/env python3
"""
简单测试前端函数
"""

import requests
import time
from pymongo import MongoClient

def get_npc_id_mapping():
    """获取大臣姓名到角色ID的映射"""
    try:
        print('🔍 开始获取NPC ID映射...')
        
        # 从统一角色集合获取角色列表
        client = MongoClient("mongodb://localhost:27017/")
        db = client["chongzhen_sim"]
        
        print('📊 连接MongoDB成功，查询unified_characters集合...')
        characters = list(db.unified_characters.find({}, {"_id": 1, "name": 1}))
        client.close()
        
        print(f'📋 从数据库获取到 {len(characters)} 个角色')
        
        # 构建映射字典
        mapping = {}
        for char in characters:
            char_name = char["name"]
            char_id = str(char["_id"])
            mapping[char_name] = char_id
            print(f'   - {char_name}: {char_id}')
        
        print(f'✅ 角色映射构建完成: {list(mapping.keys())}')
        return mapping
    
    except Exception as e:
        print(f'❌ 获取角色映射失败: {str(e)}')
        import traceback
        print(f'🔍 错误详情: {traceback.format_exc()}')
        return {}

def api_request(endpoint: str, method: str = "GET", data: dict = None):
    """发送API请求"""
    try:
        url = f"http://127.0.0.1:8000{endpoint}"
        print(f'📤 API请求: {method} {url}')
        if data:
            print(f'📝 请求数据: {data}')

        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data)

        print(f'📥 响应状态码: {response.status_code}')

        if response.status_code == 200:
            response_data = response.json()
            print(f'✅ 响应成功: {len(str(response_data))} 字符')
            return response_data
        else:
            error_text = response.text
            print(f'❌ API请求失败: {response.status_code} - {error_text}')
            return None
    except Exception as e:
        print(f'🔌 请求异常: {str(e)}')
        return None

def generate_minister_reply_fixed(minister, user_input, player_id):
    """修复后的大臣回复生成函数"""
    try:
        # 首先尝试调用LLM智能对话API
        print(f'🤖 尝试调用LLM对话API，大臣: {minister["name"]}')
        print(f'📝 当前player_id: {player_id}')
        print(f'💬 用户输入: {user_input}')

        # 根据大臣姓名查找对应的NPC ID
        npc_mapping = get_npc_id_mapping()
        npc_id = npc_mapping.get(minister["name"])
        
        print(f'🔍 NPC映射结果: {minister["name"]} -> {npc_id}')

        if npc_id and player_id:
            print(f'✅ 准备调用NPC对话API')
            print(f'   - player_id: {player_id}')
            print(f'   - npc_id: {npc_id}')
            print(f'   - message: {user_input}')
            
            # 调用NPC对话API
            chat_response = api_request("/api/npc/chat", "POST", {
                "player_id": player_id,
                "npc_id": npc_id,
                "message": user_input
            })

            print(f'📥 API响应: {chat_response}')

            if chat_response and "npc_response" in chat_response:
                print(f'✅ LLM对话成功，大臣: {minister["name"]}')
                print(f'🎭 回复内容: {chat_response["npc_response"][:100]}...')
                return chat_response["npc_response"]
            else:
                print(f'⚠️ LLM对话API返回异常，大臣: {minister["name"]}')
                print(f'   响应内容: {chat_response}')
        else:
            if not npc_id:
                print(f'⚠️ 未找到对应NPC ID，大臣: {minister["name"]}')
            if not player_id:
                print(f'⚠️ player_id为空: {player_id}')

    except Exception as e:
        print(f'❌ LLM对话调用失败: {str(e)}')
        import traceback
        print(f'🔍 错误详情: {traceback.format_exc()}')

    # 如果LLM调用失败，回退到简单模拟
    print(f'🔄 回退到简单模拟回复，大臣: {minister["name"]}')
    return f"臣遵旨。（模拟回复）"

def test_simple_frontend():
    """简单测试前端功能"""
    print("🧪 简单测试前端LLM对话功能")
    print("=" * 60)
    
    # 1. 注册测试用户
    print("\n1️⃣ 注册测试用户...")
    username = f"simple_test_{int(time.time())}"
    register_response = api_request("/api/user/register", "POST", {
        "username": username,
        "password": "test123",
        "game_difficulty": "normal"
    })
    
    if not register_response or "id" not in register_response:
        print("❌ 用户注册失败，测试终止")
        return
    
    player_id = register_response["id"]
    print(f"✅ 用户注册成功，player_id: {player_id}")
    
    # 2. 测试大臣对话
    print("\n2️⃣ 测试大臣对话...")
    
    test_minister = {"name": "温体仁", "position": "内阁首辅"}
    test_message = "爱卿，近日朝政如何？"
    
    print(f"\n--- 测试与{test_minister['name']}的对话 ---")
    print(f"👑 皇帝: {test_message}")
    
    reply = generate_minister_reply_fixed(test_minister, test_message, player_id)
    
    print(f"🎭 {test_minister['name']}回复: {reply}")
    
    if "臣遵旨" in reply and len(reply) < 20:
        print("⚠️ 返回的是简单模拟回复，LLM对话可能失败")
        return False
    else:
        print("✅ 返回的是LLM智能回复")
        return True

if __name__ == "__main__":
    success = test_simple_frontend()
    if success:
        print("\n🎉 前端LLM对话功能正常工作！")
    else:
        print("\n❌ 前端LLM对话功能需要进一步调试")
