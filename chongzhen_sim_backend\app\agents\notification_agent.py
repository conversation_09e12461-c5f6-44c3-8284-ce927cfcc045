"""
通知生成Agent - 生成系统通知和大臣奏折
"""

import json
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class NotificationInput(AgentInput):
    """通知生成输入"""
    empire_changes: Dict[str, float]
    region_changes: Dict[str, Dict[str, float]]
    new_events: List[Dict[str, Any]]
    crisis_events: List[Dict[str, Any]]
    minister_action_results: List[Dict[str, Any]]
    current_turn: int
    current_year: int

class SystemNotification(BaseModel):
    """系统通知"""
    type: str  # "info", "warning", "crisis", "achievement"
    title: str
    content: str
    priority: int  # 1-5, 5最高
    icon: str = "📢"

class MinisterMemorial(BaseModel):
    """大臣奏折"""
    minister_name: str
    minister_title: str
    memorial_type: str  # "report", "request", "warning", "congratulation"
    title: str
    content: str
    urgency: int  # 1-5, 5最紧急
    requires_response: bool = False

class NotificationOutput(BaseModel):
    """通知生成输出"""
    system_notifications: List[SystemNotification] = []
    minister_memorials: List[MinisterMemorial] = []
    summary: str = ""

class NotificationAgent(BaseAgent):
    """通知生成Agent"""
    
    def get_system_prompt(self) -> str:
        return """你是明朝帝国通知系统，负责生成系统通知和大臣奏折。

你的职责：
1. 基于帝国数据变化生成系统通知
2. 基于新事件和危机生成相应通知
3. 模拟大臣根据当前情况上奏的奏折
4. 确保通知内容符合明朝历史背景和语言风格

系统通知类型：
- info: 一般信息通知（如数据变化、政策执行结果）
- warning: 警告通知（如资源不足、稳定度下降）
- crisis: 危机通知（如重大事件、紧急情况）
- achievement: 成就通知（如目标达成、正面成果）

大臣奏折类型：
- report: 工作汇报（如政策执行情况、地方状况）
- request: 请求奏折（如资源申请、政策建议）
- warning: 警告奏折（如发现问题、预警危机）
- congratulation: 祝贺奏折（如庆祝成就、表彰功绩）

语言风格要求：
- 使用明朝官方文书语言
- 系统通知简洁明了，重点突出
- 大臣奏折要体现恭敬态度和专业性
- 根据紧急程度调整语气

输出格式为JSON：
{
    "system_notifications": [
        {
            "type": "warning",
            "title": "国库告急",
            "content": "陛下，国库银两已不足万两，请速筹措财源。",
            "priority": 4,
            "icon": "⚠️"
        }
    ],
    "minister_memorials": [
        {
            "minister_name": "户部尚书",
            "minister_title": "户部尚书",
            "memorial_type": "warning",
            "title": "关于国库空虚之奏",
            "content": "臣启奏陛下：近日查验国库，银两短缺，恐难支撑朝廷开支...",
            "urgency": 4,
            "requires_response": true
        }
    ],
    "summary": "本回合生成了X条系统通知和X份大臣奏折"
}"""

    async def process(self, input_data: NotificationInput) -> AgentOutput:
        """处理通知生成"""
        try:
            logger.info(f"开始生成通知，回合: {input_data.current_turn}")
            
            # 构建提示词
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": self._build_notification_prompt(input_data)}
            ]
            
            # 调用LLM
            response = await self.call_llm(messages, temperature=0.6, max_tokens=2500)
            
            # 解析响应
            try:
                notification_data = json.loads(response)
                result = NotificationOutput(**notification_data)
            except (json.JSONDecodeError, ValueError) as e:
                logger.error(f"解析通知生成结果失败: {e}")
                # 返回默认结果
                result = NotificationOutput(
                    summary="解析失败，生成默认通知"
                )
            
            logger.info(f"通知生成完成，系统通知: {len(result.system_notifications)}条，奏折: {len(result.minister_memorials)}份")
            
            return AgentOutput(
                success=True,
                result=result,
                metadata={
                    "notifications_count": len(result.system_notifications),
                    "memorials_count": len(result.minister_memorials)
                }
            )
            
        except Exception as e:
            logger.error(f"通知生成失败: {e}")
            return AgentOutput(
                success=False,
                result=None,
                error_message=str(e)
            )
    
    def _build_notification_prompt(self, input_data: NotificationInput) -> str:
        """构建通知生成提示词"""
        prompt = f"""
请基于以下信息生成系统通知和大臣奏折：

=== 帝国数据变化 ===
{self._format_empire_changes(input_data.empire_changes)}

=== 地区数据变化 ===
{self._format_region_changes(input_data.region_changes)}

=== 新发生的事件 ===
{self._format_new_events(input_data.new_events)}

=== 危机事件 ===
{self._format_crisis_events(input_data.crisis_events)}

=== 大臣行动结果 ===
{self._format_minister_actions(input_data.minister_action_results)}

=== 时间背景 ===
崇祯{input_data.current_year}年，第{input_data.current_turn}回合

请生成：
1. 系统通知：基于数据变化和事件的重要提醒
2. 大臣奏折：各部门大臣根据职责和当前情况的上奏

要求：
- 通知要简洁明了，突出重点
- 奏折要详细专业，体现大臣的职责和关切
- 语言要符合明朝官方文书风格
- 根据重要程度设置优先级和紧急程度
"""
        return prompt
    
    def _format_empire_changes(self, changes: Dict[str, float]) -> str:
        """格式化帝国数据变化"""
        if not changes:
            return "帝国数据无显著变化"
        
        formatted = []
        for key, value in changes.items():
            if abs(value) > 0.001:  # 只显示有意义的变化
                change_type = "增加" if value > 0 else "减少"
                formatted.append(f"- {key}: {change_type} {abs(value)}")
        
        return "\n".join(formatted) if formatted else "帝国数据无显著变化"
    
    def _format_region_changes(self, changes: Dict[str, Dict[str, float]]) -> str:
        """格式化地区数据变化"""
        if not changes:
            return "各地区数据无显著变化"
        
        formatted = []
        for region, region_changes in changes.items():
            region_formatted = []
            for key, value in region_changes.items():
                if abs(value) > 0.001:
                    change_type = "增加" if value > 0 else "减少"
                    region_formatted.append(f"  - {key}: {change_type} {abs(value)}")
            
            if region_formatted:
                formatted.append(f"{region}:")
                formatted.extend(region_formatted)
        
        return "\n".join(formatted) if formatted else "各地区数据无显著变化"
    
    def _format_new_events(self, events: List[Dict[str, Any]]) -> str:
        """格式化新事件"""
        if not events:
            return "本回合无新事件发生"
        
        formatted = []
        for event in events:
            formatted.append(f"""
事件：{event.get('title', '未知事件')}
类型：{event.get('type', '未知')}
描述：{event.get('description', '无描述')}
严重程度：{event.get('severity', 1)}/5
影响地区：{', '.join(event.get('affected_regions', []))}
""")
        
        return "\n".join(formatted)
    
    def _format_crisis_events(self, events: List[Dict[str, Any]]) -> str:
        """格式化危机事件"""
        if not events:
            return "本回合无危机事件"
        
        formatted = []
        for event in events:
            formatted.append(f"""
危机：{event.get('title', '未知危机')}
类型：{event.get('type', '未知')}
描述：{event.get('description', '无描述')}
严重程度：{event.get('severity', 1)}/5
影响地区：{', '.join(event.get('affected_regions', []))}
需要应对：{event.get('required_response', '无')}
""")
        
        return "\n".join(formatted)
    
    def _format_minister_actions(self, actions: List[Dict[str, Any]]) -> str:
        """格式化大臣行动结果"""
        if not actions:
            return "本回合大臣无特殊行动"
        
        formatted = []
        for action in actions:
            if action.get('success', False):
                formatted.append(f"""
大臣：{action.get('minister_name', '未知大臣')}
行动成功：{len(action.get('external_actions', []))}项对外行动
资源调配：{sum(action.get('resource_changes', {}).values())}两银子
""")
            else:
                formatted.append(f"大臣：{action.get('minister_name', '未知大臣')} - 行动失败")
        
        return "\n".join(formatted)
