"""
MongoDB数据库连接和管理
"""

from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import MongoClient
from typing import Optional
from app.config import settings
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

# 全局数据库连接
_client: Optional[AsyncIOMotorClient] = None
_sync_client: Optional[MongoClient] = None
_database = None

class DatabaseManager:
    """数据库管理器，类似你的ChatLogManager"""
    def __init__(self):
        self.client = None
        self.db = None
        self.players = None
        self.map_regions = None
        self.characters = None
        self.factions = None
        self.game_sessions = None
        self.edicts = None
        self.events = None
        self.conversations = None
        self.npc_profiles = None

    async def initialize(self):
        """初始化数据库连接和集合"""
        global _client, _database
        self.client = _client
        self.db = _database

        # 定义所有集合
        self.players = self.db['players']
        self.map_regions = self.db['map_regions']
        self.characters = self.db['unified_characters']  # 使用新的统一集合
        self.factions = self.db['factions']
        self.game_sessions = self.db['game_sessions']
        self.edicts = self.db['edicts']
        self.events = self.db['events']
        self.conversations = self.db['conversations']
        # 保持向后兼容
        self.npc_profiles = self.db['unified_characters']  # 指向同一个集合

        logger.info("数据库管理器初始化完成")

# 全局数据库管理器实例
db_manager = DatabaseManager()

async def connect_to_mongo():
    """连接到MongoDB"""
    global _client, _database
    try:
        _client = AsyncIOMotorClient(settings.MONGODB_URL)
        _database = _client[settings.DATABASE_NAME]

        # 测试连接
        await _client.admin.command('ping')
        logger.info(f"成功连接到MongoDB: {settings.DATABASE_NAME}")

        # 初始化数据库管理器
        await db_manager.initialize()

        # 初始化所有业务管理器
        from app.db.managers import initialize_all_managers
        await initialize_all_managers()

    except Exception as e:
        logger.error(f"连接MongoDB失败: {e}")
        raise

async def close_mongo_connection():
    """关闭MongoDB连接"""
    global _client
    if _client:
        _client.close()
        logger.info("MongoDB连接已关闭")

def get_database():
    """获取数据库实例"""
    return _database

def get_db_manager():
    """获取数据库管理器实例"""
    return db_manager

def get_sync_client():
    """获取同步客户端（用于初始化等操作）"""
    global _sync_client
    if _sync_client is None:
        _sync_client = MongoClient(settings.MONGODB_URL)
    return _sync_client

async def init_db():
    """初始化数据库"""
    try:
        logger.info("开始初始化MongoDB数据库...")

        # 连接到数据库
        await connect_to_mongo()

        # 创建索引
        await _create_indexes()

        # 初始化基础数据
        await _init_base_data()

        logger.info("MongoDB数据库初始化完成")

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

async def _create_indexes():
    """创建数据库索引"""
    try:
        # 使用数据库管理器的集合
        db_mgr = get_db_manager()

        # 玩家集合索引
        await db_mgr.players.create_index("username", unique=True)
        # email索引设置为sparse=True，这样null值不会被索引，避免重复null值问题
        await db_mgr.players.create_index("email", unique=True, sparse=True)

        # 地区集合索引
        await db_mgr.map_regions.create_index("name", unique=True)
        await db_mgr.map_regions.create_index("region_type")

        # 统一角色集合索引
        await db_mgr.characters.create_index("name", unique=True)
        await db_mgr.characters.create_index("title")
        await db_mgr.characters.create_index("is_active")
        await db_mgr.characters.create_index("game_stats.faction")
        await db_mgr.characters.create_index("game_stats.loyalty")

        # 势力集合索引
        await db_mgr.factions.create_index("name", unique=True)
        await db_mgr.factions.create_index("faction_type")

        # 诏书集合索引
        await db_mgr.edicts.create_index("player_id")
        await db_mgr.edicts.create_index("created_at")

        # 事件集合索引
        await db_mgr.events.create_index("player_id")
        await db_mgr.events.create_index("event_type")
        await db_mgr.events.create_index("created_at")

        # 对话集合索引
        await db_mgr.conversations.create_index([("player_id", 1), ("npc_id", 1), ("session_id", 1)], unique=True)
        await db_mgr.conversations.create_index("player_id")
        await db_mgr.conversations.create_index("created_at")

        logger.info("数据库索引创建完成")

    except Exception as e:
        logger.error(f"创建索引失败: {e}")
        raise

async def _init_base_data():
    """初始化基础数据"""
    try:
        db_mgr = get_db_manager()

        # 检查是否已有基础数据
        existing_regions = await db_mgr.map_regions.count_documents({})

        if existing_regions == 0:
            logger.info("初始化地图数据...")
            await _init_map_data()

        # 检查统一角色数据
        existing_characters = await db_mgr.characters.count_documents({})

        if existing_characters == 0:
            logger.info("初始化统一角色数据...")
            await _init_unified_character_data(db_mgr)

        logger.info("基础数据初始化完成")

    except Exception as e:
        logger.error(f"基础数据初始化失败: {e}")
        raise

async def _init_map_data():
    """初始化地图数据"""
    db_mgr = get_db_manager()

    regions_data = [
        {
            "name": "北京",
            "region_type": "京师",
            "population": 1200000,
            "tax_income": 500000,
            "food_production": 200000,
            "military_presence": 50000,
            "stability": 0.8,
            "prosperity": 0.8,
            "loyalty": 0.9,
            "special_features": "皇城,政治中心,文化中心",
            "current_events": "[]",
            "climate": "温带",
            "terrain": "平原",
            "strategic_importance": 10
        },
        {
            "name": "南京",
            "region_type": "留都",
            "population": 800000,
            "tax_income": 300000,
            "food_production": 400000,
            "military_presence": 20000,
            "stability": 0.7,
            "prosperity": 0.8,
            "loyalty": 0.8,
            "special_features": "江南重镇,商业发达",
            "current_events": "[]",
            "climate": "亚热带",
            "terrain": "平原",
            "strategic_importance": 8
        },
        {
            "name": "辽东",
            "region_type": "边关",
            "population": 300000,
            "tax_income": 50000,
            "food_production": 100000,
            "military_presence": 80000,
            "stability": 0.4,
            "prosperity": 0.3,
            "loyalty": 0.6,
            "special_features": "边防重地,与后金接壤",
            "current_events": "[]",
            "climate": "寒带",
            "terrain": "山地",
            "strategic_importance": 9
        },
        {
            "name": "山西",
            "region_type": "省份",
            "population": 2000000,
            "tax_income": 200000,
            "food_production": 300000,
            "military_presence": 15000,
            "stability": 0.6,
            "prosperity": 0.6,
            "loyalty": 0.7,
            "special_features": "煤炭丰富,商人众多",
            "current_events": "[]",
            "climate": "温带",
            "terrain": "山地",
            "strategic_importance": 6
        },
        {
            "name": "陕西",
            "region_type": "省份",
            "population": 1800000,
            "tax_income": 150000,
            "food_production": 250000,
            "military_presence": 25000,
            "stability": 0.3,
            "prosperity": 0.4,
            "loyalty": 0.5,
            "special_features": "民风彪悍,易生叛乱",
            "current_events": "[]",
            "climate": "温带",
            "terrain": "丘陵",
            "strategic_importance": 7
        },
        {
            "name": "江南",
            "region_type": "地区",
            "population": 3000000,
            "tax_income": 800000,
            "food_production": 600000,
            "military_presence": 30000,
            "stability": 0.8,
            "prosperity": 0.9,
            "loyalty": 0.8,
            "special_features": "鱼米之乡,赋税重地,文化昌盛",
            "current_events": "[]",
            "climate": "亚热带",
            "terrain": "平原",
            "strategic_importance": 8
        }
    ]

    await db_mgr.map_regions.insert_many(regions_data)

async def _init_unified_character_data(db_mgr):
    """初始化统一角色数据"""
    try:
        # 检查是否已有角色数据
        existing_count = await db_mgr.characters.count_documents({})
        if existing_count > 0:
            logger.info(f"已存在 {existing_count} 个角色，跳过初始化")
            return

        # 统一的角色数据（包含游戏属性和对话属性）
        unified_characters_data = [
            {
                "name": "温体仁",
                "title": "内阁首辅",
                "age": 55,
                "health": 0.8,
                "game_stats": {
                    "loyalty": 0.7,
                    "administrative_ability": 0.7,
                    "military_ability": 0.3,
                    "diplomatic_ability": 0.6,
                    "economic_ability": 0.5,
                    "faction": "保守派",
                    "political_stance": "维护传统，反对改革",
                    "personal_interests": "保持权位，避免风险",
                    "allies": [],
                    "enemies": [],
                    "personal_wealth": 50000.0,
                    "land_holdings": 100,
                    "private_army": 0,
                    "major_achievements": "长期担任内阁首辅",
                    "major_failures": "",
                    "current_location": "京师"
                },
                "dialogue_profile": {
                    "personality": "谨慎保守，善于察言观色，政治嗅觉敏锐",
                    "background": "万历四十一年进士，历任翰林院编修、侍读学士，崇祯朝内阁首辅。为人机敏，善于迎合皇帝心意。",
                    "speaking_style": "言辞谨慎，多用敬语，善于委婉表达，常以'臣以为'、'依臣之见'开头",
                    "system_prompt": "你是明朝崇祯朝内阁首辅温体仁。你性格谨慎保守，善于察言观色，政治嗅觉敏锐。你总是试图迎合皇帝的心意，避免承担风险。说话时要体现出内阁首辅的地位，但也要表现出对皇帝的恭敬。你会根据皇帝的态度调整自己的立场，优先考虑自己的政治安全。",
                    "relationship_with_emperor": "深受崇祯信任的内阁首辅，但也因谨慎保守而时常被皇帝催促"
                },
                "interaction_history": [],
                "is_active": True
            }
        ]

        # 插入统一角色数据
        await db_mgr.characters.insert_many(unified_characters_data)
        logger.info(f"成功初始化 {len(unified_characters_data)} 个统一角色")

    except Exception as e:
        logger.error(f"初始化统一角色数据失败: {e}")
        # 不抛出异常，避免影响整个初始化过程

async def reset_database():
    """重置数据库（危险操作，仅用于开发）"""
    try:
        logger.warning("正在重置MongoDB数据库...")

        # 获取同步客户端
        sync_client = get_sync_client()
        db = sync_client[settings.DATABASE_NAME]

        # 删除所有集合
        collection_names = db.list_collection_names()
        for collection_name in collection_names:
            db.drop_collection(collection_name)

        logger.warning("MongoDB数据库重置完成")

        # 重新初始化
        await init_db()

    except Exception as e:
        logger.error(f"数据库重置失败: {e}")
        raise

def backup_database(backup_path: str):
    """备份数据库"""
    try:
        import subprocess

        # 使用mongodump进行备份
        cmd = [
            "mongodump",
            "--uri", settings.MONGODB_URL,
            "--db", settings.DATABASE_NAME,
            "--out", backup_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            logger.info(f"数据库备份到: {backup_path}")
        else:
            logger.error(f"备份失败: {result.stderr}")
            raise Exception(f"备份失败: {result.stderr}")

    except FileNotFoundError:
        logger.warning("mongodump命令未找到，请安装MongoDB工具")
        raise Exception("mongodump命令未找到")
    except Exception as e:
        logger.error(f"数据库备份失败: {e}")
        raise

# 旧的初始化函数已被 _init_unified_character_data 替代
