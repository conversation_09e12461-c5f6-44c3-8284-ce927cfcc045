"""
NPC对话相关API路由
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from app.agents.roleplay_agent import RoleplayAgent, RoleplayInput
from app.db.managers import conversation_manager, player_manager
from app.db.models.conversation import NPCProfile
from app.utils.logger import setup_logger
import uuid

logger = setup_logger(__name__)
router = APIRouter(prefix="/npc", tags=["NPC对话"])

class ChatRequest(BaseModel):
    """对话请求"""
    player_id: str
    npc_id: str
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    """对话响应"""
    npc_response: str
    npc_mood: str
    session_id: str
    conversation_id: str

class NPCCreateRequest(BaseModel):
    """创建NPC请求"""
    name: str
    title: str
    personality: str
    background: str
    speaking_style: str
    system_prompt: str
    relationship_with_emperor: str = ""

@router.post("/chat")
async def chat_with_npc(player_id: str, npc_id: str, message: str, session_id: str = None):
    """与NPC对话"""
    try:
        # 验证玩家是否存在
        player = await player_manager.get_player_by_id(player_id)
        if not player:
            raise HTTPException(status_code=404, detail="玩家不存在")

        # 获取NPC档案 - 临时使用硬编码数据
        npc_profiles = {
            "wenren": {
                "id": "wenren",
                "name": "温体仁",
                "title": "内阁首辅",
                "personality": "谨慎保守，善于察言观色",
                "background": "明朝内阁首辅，深得崇祯信任",
                "speaking_style": "恭敬谨慎，措辞严谨",
                "dialogue_profile": {"relationship_with_emperor": "忠诚"}
            },
            "yanghe": {
                "id": "yanghe",
                "name": "杨嗣昌",
                "title": "兵部尚书",
                "personality": "刚直不阿，军事才能出众",
                "background": "明朝兵部尚书，负责军务",
                "speaking_style": "直言不讳，军人作风",
                "dialogue_profile": {"relationship_with_emperor": "忠诚"}
            }
        }

        if npc_id not in npc_profiles:
            raise HTTPException(status_code=404, detail="NPC不存在")

        npc_profile = type('NPCProfile', (), npc_profiles[npc_id])()

        # 生成或使用现有session_id
        session_id = session_id or str(uuid.uuid4())

        # 简化对话记录处理 - 暂时不保存历史
        recent_messages = []  # 暂时使用空的对话历史
        
        # 构建角色扮演输入
        roleplay_input = RoleplayInput(
            player_id=player_id,
            character_name=npc_profile.name,
            player_question=message,
            current_situation={
                "npc_title": npc_profile.title,
                "relationship": npc_profile.dialogue_profile.get("relationship_with_emperor", ""),
                "game_context": "明朝崇祯年间"
            },
            conversation_history=[
                {"speaker": "emperor" if msg["role"] == "user" else "npc", "content": msg["content"]}
                for msg in recent_messages
            ]
        )
        
        # 使用角色扮演Agent生成回复
        roleplay_agent = RoleplayAgent()
        agent_output = await roleplay_agent.process(roleplay_input)
        
        if not agent_output.success:
            raise HTTPException(status_code=500, detail=f"NPC回复生成失败: {agent_output.error_message}")

        # 提取NPC回复
        roleplay_result = agent_output.result
        npc_response = roleplay_result.character_response
        npc_mood = roleplay_result.character_mood
        
        # 暂时不保存对话记录

        logger.info(f"NPC对话完成: {player.username} -> {npc_profile.name}")

        return {
            "npc_response": npc_response,
            "npc_mood": npc_mood,
            "session_id": session_id,
            "conversation_id": "temp_conversation_id"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"NPC对话失败: {e}")
        raise HTTPException(status_code=500, detail="对话处理失败")

@router.post("/create", response_model=dict)
async def create_npc(npc_request: NPCCreateRequest):
    """创建新的NPC"""
    try:
        npc_data = npc_request.model_dump()
        npc_profile = await conversation_manager.create_npc_profile(npc_data)
        
        logger.info(f"创建NPC: {npc_profile.name}")
        return {"message": "NPC创建成功", "npc_id": npc_profile.id}
        
    except Exception as e:
        logger.error(f"创建NPC失败: {e}")
        raise HTTPException(status_code=500, detail="创建NPC失败")

@router.get("/list")
async def list_npcs():
    """获取所有NPC列表"""
    try:
        # TODO: 实现从数据库获取NPC列表
        # 暂时返回硬编码的NPC列表
        npcs = [
            {"id": "wenren", "name": "温体仁", "title": "内阁首辅"},
            {"id": "yanghe", "name": "杨嗣昌", "title": "兵部尚书"},
            {"id": "chongzhen", "name": "崇祯帝", "title": "皇帝"}
        ]
        return {"npcs": npcs}
    except Exception as e:
        logger.error(f"获取NPC列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取NPC列表失败")

@router.get("/{npc_id}", response_model=dict)
async def get_npc_info(npc_id: str):
    """获取NPC信息"""
    try:
        npc_profile = await conversation_manager.get_npc_profile(npc_id)
        if not npc_profile:
            raise HTTPException(status_code=404, detail="NPC不存在")
        
        return {
            "id": npc_profile.id,
            "name": npc_profile.name,
            "title": npc_profile.title,
            "personality": npc_profile.personality,
            "background": npc_profile.background,
            "speaking_style": npc_profile.speaking_style,
            "relationship_with_emperor": npc_profile.relationship_with_emperor,
            "is_active": npc_profile.is_active
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取NPC信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取NPC信息失败")

@router.get("/conversations/{player_id}")
async def get_player_conversations(player_id: str, limit: int = 10):
    """获取玩家的对话历史"""
    try:
        # 验证玩家是否存在
        player = await player_manager.get_player_by_id(player_id)
        if not player:
            raise HTTPException(status_code=404, detail="玩家不存在")
        
        # 这里应该实现获取对话历史的逻辑
        # 暂时返回空列表
        return {"conversations": []}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取对话历史失败")
