#!/bin/bash

# 崇祯模拟器后端启动脚本

echo "正在启动崇祯模拟器后端..."

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "错误: 未找到Python环境"
    exit 1
fi

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "警告: 未检测到虚拟环境，建议使用虚拟环境运行"
fi

# 检查依赖
echo "检查依赖..."
pip install -r requirements.txt

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "警告: 未找到.env文件，将使用默认配置"
fi

# 创建日志目录
mkdir -p logs

# 初始化数据库
echo "初始化数据库..."
python -c "
import asyncio
from app.db.database import init_db
asyncio.run(init_db())
"

if [ $? -ne 0 ]; then
    echo "错误: 数据库初始化失败"
    exit 1
fi

echo "数据库初始化完成"

# 启动服务器
echo "启动FastAPI服务器..."
echo "访问地址: http://localhost:8000"
echo "API文档: http://localhost:8000/docs"
echo "按 Ctrl+C 停止服务器"

python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
