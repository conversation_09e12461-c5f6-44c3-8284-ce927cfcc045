version: '3.8'

services:
  # 崇祯模拟器后端
  chongzhen-backend:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*********************************************/chongzhen_sim
      - VECTOR_DB_URL=http://qdrant:6333
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - postgres
      - qdrant
    restart: unless-stopped
    networks:
      - chongzhen-network

  # PostgreSQL数据库
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=chongzhen_sim
      - POSTGRES_USER=chongzhen
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - chongzhen-network

  # Qdrant向量数据库
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped
    networks:
      - chongzhen-network

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - chongzhen-network

volumes:
  postgres_data:
  qdrant_data:
  redis_data:

networks:
  chongzhen-network:
    driver: bridge
