#!/usr/bin/env python3
"""
崇祯模拟器完整启动脚本
同时启动后端API服务器和前端UI
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import streamlit
        import plotly
        import pandas
        import numpy
        import requests
        import pymongo
        import motor
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    try:
        # 安装后端依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pymongo", "motor"])
        
        # 安装前端依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "ui/requirements.txt"])
        
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def start_backend():
    """启动后端服务器"""
    print("🚀 启动后端API服务器...")
    try:
        backend_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "app.main:app",
            "--reload", "--host", "127.0.0.1", "--port", "8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待后端启动
        time.sleep(3)
        
        if backend_process.poll() is None:
            print("✅ 后端服务器启动成功 - http://127.0.0.1:8000")
            return backend_process
        else:
            print("❌ 后端服务器启动失败")
            return None
    except Exception as e:
        print(f"❌ 启动后端失败: {e}")
        return None

def start_frontend():
    """启动前端UI"""
    print("🌐 启动前端UI...")
    try:
        frontend_process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "ui/main.py",
            "--server.port", "8501", "--server.address", "127.0.0.1"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待前端启动
        time.sleep(3)
        
        if frontend_process.poll() is None:
            print("✅ 前端UI启动成功 - http://127.0.0.1:8501")
            return frontend_process
        else:
            print("❌ 前端UI启动失败")
            return None
    except Exception as e:
        print(f"❌ 启动前端失败: {e}")
        return None

def main():
    """主函数"""
    print("👑 崇祯模拟器启动器 👑")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("app") or not os.path.exists("ui"):
        print("❌ 请在项目根目录下运行此脚本")
        return
    
    # 检查依赖
    if not check_dependencies():
        if not install_dependencies():
            return
    
    # 启动后端
    backend_process = start_backend()
    if not backend_process:
        print("❌ 无法启动后端服务器")
        return
    
    # 启动前端
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ 无法启动前端UI")
        if backend_process:
            backend_process.terminate()
        return
    
    print("\n" + "=" * 50)
    print("🎉 崇祯模拟器启动完成！")
    print("📱 前端UI: http://127.0.0.1:8501")
    print("🔧 后端API: http://127.0.0.1:8000")
    print("📚 API文档: http://127.0.0.1:8000/docs")
    print("=" * 50)
    print("按 Ctrl+C 停止所有服务")
    
    try:
        # 等待用户中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        
        if frontend_process:
            frontend_process.terminate()
            print("✅ 前端UI已停止")
        
        if backend_process:
            backend_process.terminate()
            print("✅ 后端API已停止")
        
        print("👋 崇祯模拟器已关闭")

if __name__ == "__main__":
    main()
