"""
输出生成Agent - 负责生成玩家可见的最终输出文本
"""

from typing import Dict, Any, List
from pydantic import BaseModel
from app.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class OutputGenerationInput(AgentInput):
    """输出生成输入"""
    simulation_narrative: str
    world_narrative: str
    data_changes: Dict[str, Any]
    current_turn: int
    current_year: int
    player_perspective: str = "emperor"  # 玩家视角

class OutputGenerationOutput(BaseModel):
    """输出生成输出"""
    title: str
    main_content: str
    data_summary: str
    next_turn_preview: str
    hidden_info: Dict[str, Any] = {}  # 玩家不可见信息

class OutputGenerationAgent(BaseAgent):
    """输出生成Agent"""
    
    def get_system_prompt(self) -> str:
        return """你是明朝皇宫的起居注官，负责为皇帝整理每月的朝政总结。

你的职责：
1. 将复杂的推演结果整理成适合皇帝阅读的报告
2. 根据皇帝视角过滤和组织信息
3. 隐藏皇帝不应该知道的机密信息
4. 生成引人入胜的叙事内容
5. 提供清晰的数据变化总结

输出要求：
- 语言风格符合明朝文风，但要通俗易懂
- 突出重要事件和变化
- 数据变化要清晰明了
- 适当营造紧张感和代入感
- 隐藏敌方机密和未来剧透

皇帝视角特点：
- 关注帝国整体状况
- 重视财政和军事
- 关心民心和官员忠诚
- 对边疆和叛乱敏感"""

    async def process(self, input_data: OutputGenerationInput) -> AgentOutput:
        """处理输出生成"""
        try:
            if not await self.validate_input(input_data):
                return self.create_error_output("输入数据无效")
            
            # 构建输出生成消息
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": self._build_output_prompt(input_data)}
            ]
            
            # 调用LLM生成输出
            response = await self.call_llm(messages, temperature=0.8, max_tokens=3000)
            
            # 解析输出结果
            output_result = self._parse_output_response(response, input_data)
            
            logger.info(f"输出生成完成，玩家ID: {input_data.player_id}, 回合: {input_data.current_turn}")
            return self.create_success_output(output_result)
            
        except Exception as e:
            logger.error(f"输出生成失败: {e}")
            return self.create_error_output(f"输出生成失败: {str(e)}")
    
    def _build_output_prompt(self, input_data: OutputGenerationInput) -> str:
        """构建输出生成提示"""
        data_changes_str = self.format_context(input_data.data_changes)
        
        return f"""
当前时间：崇祯{input_data.current_year}年第{input_data.current_turn}月

推演叙事：
{input_data.simulation_narrative}

世界事件：
{input_data.world_narrative}

数据变化：
{data_changes_str}

请为皇帝生成本月的朝政总结，输出格式如下：

【标题】
（本月总结的标题）

【主要内容】
（详细的叙事内容，包括重要事件和变化）

【数据总结】
（清晰的数据变化总结）

【下月展望】
（对下个月可能发生事件的预告）

要求：
1. 语言生动有趣，符合历史背景
2. 突出重要变化和事件
3. 适当隐藏敌方机密信息
4. 营造适度的紧张感
"""
    
    def _parse_output_response(
        self, 
        response: str, 
        input_data: OutputGenerationInput
    ) -> OutputGenerationOutput:
        """解析输出响应"""
        # 初始化输出部分
        title = f"崇祯{input_data.current_year}年第{input_data.current_turn}月朝政总结"
        main_content = ""
        data_summary = ""
        next_turn_preview = ""
        
        # 解析响应内容
        lines = response.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if '【标题】' in line:
                current_section = 'title'
            elif '【主要内容】' in line:
                current_section = 'main_content'
            elif '【数据总结】' in line:
                current_section = 'data_summary'
            elif '【下月展望】' in line:
                current_section = 'next_turn_preview'
            elif line and current_section:
                if current_section == 'title':
                    title = line
                elif current_section == 'main_content':
                    main_content += line + "\n"
                elif current_section == 'data_summary':
                    data_summary += line + "\n"
                elif current_section == 'next_turn_preview':
                    next_turn_preview += line + "\n"
        
        # 如果解析失败，使用原始响应
        if not main_content:
            main_content = response
        
        return OutputGenerationOutput(
            title=title,
            main_content=main_content.strip(),
            data_summary=data_summary.strip(),
            next_turn_preview=next_turn_preview.strip(),
            hidden_info=self._extract_hidden_info(input_data)
        )
    
    def _extract_hidden_info(self, input_data: OutputGenerationInput) -> Dict[str, Any]:
        """提取隐藏信息"""
        # 这里可以提取一些玩家不应该看到的信息
        hidden_info = {
            "full_simulation": input_data.simulation_narrative,
            "full_world_events": input_data.world_narrative,
            "raw_data_changes": input_data.data_changes
        }
        return hidden_info
