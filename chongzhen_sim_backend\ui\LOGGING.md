# 崇祯模拟器前端日志系统

## 📋 概述

为了更好地调试和监控前端应用，我们为崇祯模拟器前端添加了完整的日志系统。该系统记录用户的所有操作、API交互和系统状态变化。

## 🎯 功能特性

### ✅ 已实现的日志功能

1. **API请求日志** 🌐
   - 记录所有API请求的URL、方法和数据
   - 记录响应状态码和响应时间
   - 区分成功和失败的请求

2. **用户认证日志** 🔐
   - 记录登录尝试和结果
   - 记录注册操作
   - 记录用户退出登录

3. **页面导航日志** 📄
   - 记录用户在不同页面间的导航
   - 记录页面渲染事件

4. **大臣对话日志** 💬
   - 记录召见的大臣
   - 记录对话内容（前50字符）
   - 记录对话结束

5. **诏书操作日志** 📜
   - 记录诏书模板使用
   - 记录AI润色操作
   - 记录诏书提交
   - 记录草稿保存和清空

6. **系统状态日志** ℹ️
   - 记录应用启动
   - 记录会话状态初始化
   - 记录成功、警告和错误信息

## 🛠️ 技术实现

### 日志系统架构

```
ui/
├── logger.py          # 日志配置和工具函数
├── main.py           # 主应用（集成日志）
├── log_viewer.py     # 日志查看器
├── test_logs.py      # 日志测试脚本
└── start_with_logs.py # 带日志的启动脚本
```

### 核心组件

#### 1. logger.py
```python
from logger import log_ui_event, ui_logger

# 记录事件
log_ui_event('api', 'API请求', {'url': '/api/test'})
log_ui_event('auth', '用户登录', {'username': 'user'})
```

#### 2. 日志类型
- `api` - API请求和响应 🌐
- `auth` - 认证相关操作 🔐
- `nav` - 页面导航 📄
- `chat` - 大臣对话 💬
- `edict` - 诏书操作 📜
- `success` - 成功操作 ✅
- `warning` - 警告信息 ⚠️
- `error` - 错误信息 ❌
- `info` - 一般信息 ℹ️

#### 3. 日志格式
```
16:44:29 - 🎮 UI - INFO - 🌐 API请求: GET /api/user/profile | 数据: {'player_id': '123'}
16:44:30 - 🎮 UI - INFO - ✅ 登录成功，玩家ID: 68ad70b651a41b72eb135bed
16:44:31 - 🎮 UI - INFO - 📄 导航到主界面
```

## 🚀 使用方法

### 1. 基本使用
```bash
# 启动主应用（包含日志）
streamlit run main.py --server.port 8501

# 查看控制台日志输出
```

### 2. 使用日志查看器
```bash
# 启动日志查看器
streamlit run log_viewer.py --server.port 8502

# 访问 http://localhost:8502 查看可视化日志
```

### 3. 同时启动主应用和日志查看器
```bash
python start_with_logs.py
```
这将：
- 启动主应用在端口8501
- 启动日志查看器在端口8502
- 自动打开两个浏览器窗口

### 4. 测试日志功能
```bash
python test_logs.py
```

## 📊 日志查看器功能

### 实时监控
- ✅ 自动刷新日志显示
- ✅ 可配置刷新间隔
- ✅ 实时统计信息

### 过滤功能
- ✅ 按日志类型过滤
- ✅ 显示/隐藏特定类型日志
- ✅ 清空显示缓存

### 可视化
- ✅ 彩色编码不同类型日志
- ✅ 时间戳显示
- ✅ 图标标识
- ✅ 统计图表

## 🔧 配置选项

### 日志级别
可以在`logger.py`中调整日志级别：
```python
logger.setLevel(logging.DEBUG)  # 显示所有日志
logger.setLevel(logging.INFO)   # 显示INFO及以上
logger.setLevel(logging.WARNING) # 只显示警告和错误
```

### 日志格式
可以自定义日志格式：
```python
formatter = logging.Formatter(
    '%(asctime)s - 🎮 UI - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
```

## 🐛 调试技巧

### 1. API调试
查看API请求日志，确认：
- 请求URL是否正确
- 请求数据格式是否正确
- 响应状态码

### 2. 认证调试
查看认证日志，确认：
- 用户名密码是否正确传递
- 登录流程是否正常
- 会话状态是否正确设置

### 3. 导航调试
查看导航日志，确认：
- 页面跳转是否正常
- 会话状态是否保持

## 📈 性能监控

日志系统还可以用于性能监控：
- API响应时间
- 页面加载时间
- 用户操作频率
- 错误发生率

## 🔮 未来扩展

计划添加的功能：
- [ ] 日志持久化到文件
- [ ] 日志分析和报告
- [ ] 性能指标收集
- [ ] 用户行为分析
- [ ] 错误自动报告
- [ ] 日志搜索功能

## 📝 示例日志输出

```
16:44:29 - 🎮 UI - INFO - 🚀 崇祯模拟器前端启动
16:44:29 - 🎮 UI - INFO - 🔗 API基础URL: http://127.0.0.1:8000
16:44:29 - 🎮 UI - INFO - 🔄 初始化会话状态...
16:44:30 - 🎮 UI - INFO - 🔐 显示登录页面
16:44:35 - 🎮 UI - INFO - 🌐 请求: POST http://127.0.0.1:8000/api/user/login | 数据: {'username': 'test', 'password': '***'}
16:44:35 - 🎮 UI - INFO - ✅ 登录成功，玩家ID: 68ad70b651a41b72eb135bed
16:44:35 - 🎮 UI - INFO - 📄 渲染页面: 主界面
16:44:40 - 🎮 UI - INFO - 👥 导航到召见大臣页面
16:44:42 - 🎮 UI - INFO - 👥 选择召见大臣: 温体仁 (内阁首辅)
16:44:45 - 🎮 UI - INFO - 💬 用户对温体仁说: 当前国库状况如何？...
16:44:45 - 🎮 UI - INFO - 🎭 温体仁回复: 陛下，国库确实有些紧张，建议节省开支...
```

这个完整的日志系统让你能够：
1. **实时监控**前端应用的运行状态
2. **快速定位**问题和错误
3. **分析用户行为**和使用模式
4. **优化性能**和用户体验
5. **调试API交互**问题
