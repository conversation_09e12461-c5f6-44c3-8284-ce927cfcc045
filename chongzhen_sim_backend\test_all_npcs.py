#!/usr/bin/env python3
"""
测试所有NPC的对话功能
"""

import requests
import json
import time
from pymongo import MongoClient

# 服务器配置
BASE_URL = "http://127.0.0.1:8000"

def get_npc_list():
    """获取NPC列表"""
    try:
        client = MongoClient("mongodb://localhost:27017/")
        db = client["chongzhen_sim"]
        npcs = list(db.npc_profiles.find({}, {"_id": 1, "name": 1, "title": 1}))
        client.close()
        return npcs
    except Exception as e:
        print(f"获取NPC列表失败: {e}")
        return []

def register_test_user():
    """注册测试用户"""
    try:
        username = f"test_emperor_{int(time.time())}"
        response = requests.post(f"{BASE_URL}/api/user/register", json={
            "username": username,
            "password": "test123",
            "game_difficulty": "normal"
        })
        
        if response.status_code == 200:
            result = response.json()
            return result.get("id")
        else:
            print(f"用户注册失败: {response.status_code}, {response.text}")
            return None
    except Exception as e:
        print(f"注册用户时出错: {e}")
        return None

def test_npc_chat(player_id, npc_id, npc_name, message):
    """测试NPC对话"""
    try:
        response = requests.post(f"{BASE_URL}/api/npc/chat", json={
            "player_id": player_id,
            "npc_id": npc_id,
            "message": message
        })
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n【{npc_name}】回复:")
            print(f"内容: {result['npc_response']}")
            print(f"心情: {result['npc_mood']}")
            print("-" * 80)
            return result
        else:
            print(f"与{npc_name}对话失败: {response.status_code}, {response.text}")
            return None
    except Exception as e:
        print(f"测试与{npc_name}对话时出错: {e}")
        return None

def main():
    """主测试函数"""
    print("=== 崇祯模拟器 - 所有NPC对话测试 ===\n")
    
    # 获取NPC列表
    npcs = get_npc_list()
    if not npcs:
        print("无法获取NPC列表，退出测试")
        return
    
    # 注册测试用户
    player_id = register_test_user()
    if not player_id:
        print("无法注册测试用户，退出测试")
        return
    
    print(f"测试用户ID: {player_id}\n")
    
    # 测试问题
    test_questions = [
        "爱卿，朕想听听你对当前朝政的看法。",
        "边防告急，你认为应该如何应对？",
        "国库空虚，有何良策？"
    ]
    
    # 与每个NPC对话
    for npc in npcs:
        npc_id = str(npc["_id"])
        npc_name = npc["name"]
        npc_title = npc["title"]
        
        print(f"\n{'='*60}")
        print(f"开始与 {npc_name}（{npc_title}）对话")
        print(f"{'='*60}")
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n--- 第{i}个问题 ---")
            print(f"皇帝: {question}")
            
            result = test_npc_chat(player_id, npc_id, npc_name, question)
            if not result:
                print(f"与{npc_name}的对话失败，跳过后续问题")
                break
            
            # 稍微延迟，避免请求过快
            time.sleep(1)
    
    print(f"\n{'='*60}")
    print("所有NPC对话测试完成！")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
