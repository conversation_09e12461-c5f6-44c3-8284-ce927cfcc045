from openai import OpenAI

if __name__ == "__main__":
    URL = "https://aigc-api.apps-hangyan.danlu.netease.com/v1"
    KEY = "sk-FYHWhPEuIvF65GBsFTH9324QpAZb9iWCNOsNCwkBx7AQkxAJ"
    client = OpenAI(base_url=URL, api_key=KEY)
    response = client.chat.completions.create(
        max_tokens=4096,
        # 这里指定模型名称，目前就用qwen-plus吧
        # model="gpt-4.1",
        model="qwen-plus",
        messages=[
            {
                "role": "user",
                "content": "你是谁？",
            }
        ],
    )
    print(response.json())
