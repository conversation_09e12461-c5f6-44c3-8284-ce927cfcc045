"""
事件总线 - 用于解耦组件间的通信
"""

import asyncio
from typing import Dict, List, Callable, Any
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class EventBus:
    """事件总线"""
    
    def __init__(self):
        self._subscribers: Dict[str, List[Callable]] = {}
        self._event_queue = asyncio.Queue()
        self._running = False
    
    def subscribe(self, event_type: str, handler: Callable):
        """订阅事件"""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        
        self._subscribers[event_type].append(handler)
        logger.debug(f"订阅事件: {event_type}")
    
    def unsubscribe(self, event_type: str, handler: Callable):
        """取消订阅"""
        if event_type in self._subscribers:
            try:
                self._subscribers[event_type].remove(handler)
                logger.debug(f"取消订阅事件: {event_type}")
            except ValueError:
                pass
    
    async def publish(self, event_type: str, data: Any = None):
        """发布事件"""
        event = {
            "type": event_type,
            "data": data,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        await self._event_queue.put(event)
        logger.debug(f"发布事件: {event_type}")
    
    async def start(self):
        """启动事件处理"""
        self._running = True
        logger.info("事件总线启动")
        
        while self._running:
            try:
                # 等待事件
                event = await asyncio.wait_for(
                    self._event_queue.get(),
                    timeout=1.0
                )
                
                # 处理事件
                await self._handle_event(event)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"事件处理异常: {e}")
    
    async def stop(self):
        """停止事件处理"""
        self._running = False
        logger.info("事件总线停止")
    
    async def _handle_event(self, event: Dict[str, Any]):
        """处理单个事件"""
        event_type = event["type"]
        event_data = event["data"]
        
        if event_type not in self._subscribers:
            return
        
        # 并发执行所有处理器
        handlers = self._subscribers[event_type]
        tasks = []
        
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    task = asyncio.create_task(handler(event_data))
                else:
                    task = asyncio.create_task(
                        asyncio.get_event_loop().run_in_executor(
                            None, handler, event_data
                        )
                    )
                tasks.append(task)
            except Exception as e:
                logger.error(f"创建事件处理任务失败: {e}")
        
        if tasks:
            try:
                await asyncio.gather(*tasks, return_exceptions=True)
            except Exception as e:
                logger.error(f"事件处理失败: {e}")

# 全局事件总线实例
_global_event_bus = None

def get_event_bus() -> EventBus:
    """获取全局事件总线"""
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = EventBus()
    return _global_event_bus

# 便捷函数
async def publish_event(event_type: str, data: Any = None):
    """发布事件"""
    bus = get_event_bus()
    await bus.publish(event_type, data)

def subscribe_event(event_type: str, handler: Callable):
    """订阅事件"""
    bus = get_event_bus()
    bus.subscribe(event_type, handler)

# 预定义的事件类型
class GameEvents:
    """游戏事件类型"""
    TURN_START = "turn_start"
    TURN_END = "turn_end"
    EDICT_SUBMITTED = "edict_submitted"
    EDICT_EXECUTED = "edict_executed"
    CRISIS_TRIGGERED = "crisis_triggered"
    CRISIS_RESOLVED = "crisis_resolved"
    CHARACTER_INTERACTION = "character_interaction"
    LOYALTY_CHANGED = "loyalty_changed"
    RESOURCE_CHANGED = "resource_changed"
    GAME_OVER = "game_over"

# 事件处理器装饰器
def event_handler(event_type: str):
    """事件处理器装饰器"""
    def decorator(func: Callable):
        subscribe_event(event_type, func)
        return func
    return decorator
