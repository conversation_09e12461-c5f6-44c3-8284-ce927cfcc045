# 崇祯模拟器 - 完整版

基于LLM的明朝崇祯皇帝模拟器，包含后端API和前端Web界面。

## 🎯 项目概述

这是一个历史模拟游戏，玩家扮演明朝崇祯皇帝，通过颁布诏书、召见大臣、管理国家来体验明末的历史情境。项目采用现代化的技术栈，包括：

- **后端**: FastAPI + MongoDB + Motor (异步)
- **前端**: Streamlit + Plotly + Pandas
- **AI集成**: 支持LLM集成用于智能建议和内容生成

## 🚀 快速开始

### 方法一：一键启动（推荐）

```bash
python start_all.py
```

这将自动：
- 检查并安装依赖
- 启动后端API服务器 (http://127.0.0.1:8000)
- 启动前端Web界面 (http://127.0.0.1:8501)

### 方法二：分别启动

#### 启动后端
```bash
# 安装后端依赖
pip install pymongo motor fastapi uvicorn

# 启动后端服务器
python -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

#### 启动前端
```bash
# 进入UI目录
cd ui

# 安装前端依赖
pip install -r requirements.txt

# 启动前端界面
streamlit run main.py --server.port 8501
```

## 🎮 功能特性

### 🏠 主界面
- **游戏概览**: 显示当前回合、年份、游戏状态
- **事件展示**: 展示最近发生的重要事件
- **快速操作**: 一键推进回合

### 👤 崇祯个人数据
- **皇帝属性**: 年龄、健康、洞察力、魅力、智力
- **游戏统计**: 颁布诏书数、召见大臣次数、解决危机数
- **能力雷达图**: 直观展示皇帝各项能力

### 🏛️ 大明势力数据
- **资源状况**: 国库银两、粮食储备、军队数量、人口总数
- **趋势分析**: 资源变化趋势图表
- **实时监控**: 动态更新的数据指标

### 🗺️ 地图系统
- **地区信息**: 北京、南京、辽东、山西、陕西、江南等重要地区
- **详细数据**: 人口、稳定度、繁荣度、忠诚度
- **可视化对比**: 各地区指标对比图表
- **交互体验**: 鼠标悬停查看详细信息

### 👥 召见大臣
- **大臣列表**: 温体仁、袁崇焕、魏忠贤、孙承宗等历史人物
- **属性展示**: 忠诚度、能力、派系信息
- **实时对话**: 与大臣进行文字对话
- **智能回复**: 基于历史人物性格的模拟回复

### 📜 诏书写作工具
- **模板系统**: 赈灾、军事、税收、人事、改革等多种诏书模板
- **AI助手**: 智能建议系统，提供政治、经济、军事、民生、外交建议
- **内容润色**: AI辅助润色诏书内容
- **影响预测**: 预测诏书对民心、国库、军心、稳定的影响
- **历史记录**: 查看历史颁布的诏书

## 🛠️ 技术架构

### 后端架构
```
app/
├── main.py              # FastAPI应用入口
├── config.py            # 配置管理
├── db/
│   ├── database.py      # MongoDB连接管理
│   ├── models/          # 数据模型
│   └── repositories/    # 数据访问层
├── api/                 # API路由
│   ├── user_routes.py   # 用户相关API
│   ├── game_routes.py   # 游戏核心API
│   └── admin_routes.py  # 管理员API
├── schemas/             # Pydantic模式
├── services/            # 业务逻辑层
└── utils/               # 工具函数
```

### 前端架构
```
ui/
├── main.py              # Streamlit主应用
├── config.py            # UI配置
├── requirements.txt     # 前端依赖
├── run_ui.py           # UI启动脚本
└── README.md           # UI文档
```

## 📊 数据模型

### 玩家模型
- 基本信息：用户名、密码、邮箱
- 游戏进度：当前回合、年份、游戏状态
- 皇帝属性：年龄、健康、洞察力、魅力、智力
- 统计数据：诏书数、召见次数、危机解决数

### 地区模型
- 基本信息：名称、类型、人口
- 经济数据：税收、粮食产量、繁荣度
- 政治数据：稳定度、忠诚度、军事存在
- 特殊属性：气候、地形、战略重要性

### 角色模型
- 基本信息：姓名、职位、年龄、健康
- 能力属性：行政、军事、外交、经济能力
- 政治属性：忠诚度、派系、政治立场
- 关系网络：盟友、敌人、互动历史

## 🎨 界面设计

### 视觉风格
- **古典主题**: 采用明朝风格的配色方案
- **现代交互**: 现代化的UI组件和交互方式
- **数据可视化**: 丰富的图表和可视化元素

### 用户体验
- **直观导航**: 清晰的侧边栏导航
- **响应式设计**: 适配不同屏幕尺寸
- **实时反馈**: 操作结果的即时反馈
- **渐进式披露**: 分层展示信息，避免信息过载

## 🔧 配置说明

### 环境变量
```bash
# MongoDB配置
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=chongzhen_sim

# API配置
API_BASE_URL=http://127.0.0.1:8000
```

### 自定义配置
可以在 `ui/config.py` 中自定义：
- 颜色主题
- 默认数据
- 诏书模板
- AI建议内容

## 📱 使用指南

1. **注册登录**: 首次使用需要注册账号
2. **查看概览**: 了解当前游戏状态
3. **管理国家**: 查看地区和资源状况
4. **召见大臣**: 与历史人物对话获取建议
5. **颁布诏书**: 使用AI助手编写和润色诏书
6. **推进回合**: 观察决策的影响和结果

## 🚧 开发计划

- [ ] 集成真实的LLM API
- [ ] 添加更多历史事件
- [ ] 实现存档系统
- [ ] 添加多人游戏支持
- [ ] 优化AI建议算法
- [ ] 增加更多可视化图表

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
