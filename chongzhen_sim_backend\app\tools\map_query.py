"""
地图查询工具 - 查询地块状态和地理信息
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.tools.registry import tool
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class RegionInfo(BaseModel):
    """地区信息"""
    name: str
    type: str  # 省份、府、县、边关等
    population: int
    tax_income: float
    food_production: float
    military_presence: int
    stability: float
    special_features: List[str] = []
    current_events: List[str] = []

class MapQueryResult(BaseModel):
    """地图查询结果"""
    regions: List[RegionInfo]
    total_population: int
    total_tax_income: float
    total_food_production: float
    summary: str

@tool("map_query", "查询地图和地区信息")
async def query_regions(
    region_names: List[str] = None,
    region_type: str = None,
    query_all: bool = False
) -> MapQueryResult:
    """查询地区信息"""
    
    try:
        # 预定义的地区数据（实际应该从数据库查询）
        all_regions = _get_all_regions_data()
        
        # 筛选查询结果
        if query_all:
            selected_regions = all_regions
        elif region_names:
            selected_regions = [r for r in all_regions if r.name in region_names]
        elif region_type:
            selected_regions = [r for r in all_regions if r.type == region_type]
        else:
            # 默认返回重要地区
            important_regions = ["北京", "南京", "西安", "广州", "辽东"]
            selected_regions = [r for r in all_regions if r.name in important_regions]
        
        # 计算汇总信息
        total_population = sum(r.population for r in selected_regions)
        total_tax_income = sum(r.tax_income for r in selected_regions)
        total_food_production = sum(r.food_production for r in selected_regions)
        
        # 生成摘要
        summary = _generate_summary(selected_regions, total_population, total_tax_income)
        
        result = MapQueryResult(
            regions=selected_regions,
            total_population=total_population,
            total_tax_income=total_tax_income,
            total_food_production=total_food_production,
            summary=summary
        )
        
        logger.info(f"地图查询完成，返回 {len(selected_regions)} 个地区")
        return result
        
    except Exception as e:
        logger.error(f"地图查询失败: {e}")
        return MapQueryResult(
            regions=[],
            total_population=0,
            total_tax_income=0.0,
            total_food_production=0.0,
            summary=f"查询失败: {str(e)}"
        )

def _get_all_regions_data() -> List[RegionInfo]:
    """获取所有地区数据"""
    
    regions_data = [
        {
            "name": "北京",
            "type": "京师",
            "population": 1200000,
            "tax_income": 500000,
            "food_production": 200000,
            "military_presence": 50000,
            "stability": 0.8,
            "special_features": ["皇城", "政治中心", "文化中心"],
            "current_events": ["朝廷议政", "科举考试"]
        },
        {
            "name": "南京",
            "type": "留都",
            "population": 800000,
            "tax_income": 300000,
            "food_production": 400000,
            "military_presence": 20000,
            "stability": 0.7,
            "special_features": ["江南重镇", "商业发达"],
            "current_events": ["商贸繁荣"]
        },
        {
            "name": "辽东",
            "type": "边关",
            "population": 300000,
            "tax_income": 50000,
            "food_production": 100000,
            "military_presence": 80000,
            "stability": 0.4,
            "special_features": ["边防重地", "与后金接壤"],
            "current_events": ["边境紧张", "军情频发"]
        },
        {
            "name": "山西",
            "type": "省份",
            "population": 2000000,
            "tax_income": 200000,
            "food_production": 300000,
            "military_presence": 15000,
            "stability": 0.6,
            "special_features": ["煤炭丰富", "商人众多"],
            "current_events": ["商业活跃"]
        },
        {
            "name": "陕西",
            "type": "省份",
            "population": 1800000,
            "tax_income": 150000,
            "food_production": 250000,
            "military_presence": 25000,
            "stability": 0.3,
            "special_features": ["民风彪悍", "易生叛乱"],
            "current_events": ["流民聚集", "治安不稳"]
        },
        {
            "name": "江南",
            "type": "地区",
            "population": 3000000,
            "tax_income": 800000,
            "food_production": 600000,
            "military_presence": 30000,
            "stability": 0.8,
            "special_features": ["鱼米之乡", "赋税重地", "文化昌盛"],
            "current_events": ["农业丰收", "文人聚集"]
        },
        {
            "name": "广东",
            "type": "省份",
            "population": 1500000,
            "tax_income": 400000,
            "food_production": 350000,
            "military_presence": 20000,
            "stability": 0.7,
            "special_features": ["海贸发达", "外商聚集"],
            "current_events": ["海外贸易", "传教士活动"]
        },
        {
            "name": "四川",
            "type": "省份",
            "population": 2500000,
            "tax_income": 300000,
            "food_production": 500000,
            "military_presence": 25000,
            "stability": 0.6,
            "special_features": ["天府之国", "地势险要"],
            "current_events": ["农业稳定"]
        }
    ]
    
    return [RegionInfo(**data) for data in regions_data]

def _generate_summary(regions: List[RegionInfo], total_population: int, total_tax_income: float) -> str:
    """生成查询摘要"""
    
    if not regions:
        return "未查询到相关地区信息"
    
    # 统计不同类型地区
    region_types = {}
    for region in regions:
        region_types[region.type] = region_types.get(region.type, 0) + 1
    
    # 找出最重要的地区
    most_populous = max(regions, key=lambda r: r.population)
    highest_tax = max(regions, key=lambda r: r.tax_income)
    
    # 统计稳定性
    stable_regions = [r for r in regions if r.stability >= 0.7]
    unstable_regions = [r for r in regions if r.stability < 0.5]
    
    summary_parts = [
        f"查询到 {len(regions)} 个地区",
        f"总人口 {total_population:,} 人",
        f"总税收 {total_tax_income:,} 两银子",
        f"人口最多的地区是 {most_populous.name} ({most_populous.population:,} 人)",
        f"税收最高的地区是 {highest_tax.name} ({highest_tax.tax_income:,} 两)",
    ]
    
    if stable_regions:
        summary_parts.append(f"稳定地区 {len(stable_regions)} 个: {', '.join([r.name for r in stable_regions[:3]])}")
    
    if unstable_regions:
        summary_parts.append(f"不稳定地区 {len(unstable_regions)} 个: {', '.join([r.name for r in unstable_regions[:3]])}")
    
    return "；".join(summary_parts)

@tool("region_detail_query", "查询特定地区详细信息")
async def query_region_detail(region_name: str) -> Dict[str, Any]:
    """查询特定地区的详细信息"""
    
    try:
        all_regions = _get_all_regions_data()
        target_region = None
        
        for region in all_regions:
            if region.name == region_name:
                target_region = region
                break
        
        if not target_region:
            return {
                "found": False,
                "message": f"未找到地区: {region_name}"
            }
        
        # 生成详细信息
        detail_info = {
            "found": True,
            "basic_info": {
                "name": target_region.name,
                "type": target_region.type,
                "population": target_region.population,
                "tax_income": target_region.tax_income,
                "food_production": target_region.food_production,
                "military_presence": target_region.military_presence,
                "stability": target_region.stability
            },
            "special_features": target_region.special_features,
            "current_events": target_region.current_events,
            "analysis": _analyze_region(target_region)
        }
        
        logger.info(f"查询地区详情: {region_name}")
        return detail_info
        
    except Exception as e:
        logger.error(f"地区详情查询失败: {e}")
        return {
            "found": False,
            "message": f"查询失败: {str(e)}"
        }

def _analyze_region(region: RegionInfo) -> Dict[str, Any]:
    """分析地区状况"""
    
    analysis = {
        "economic_status": "一般",
        "security_status": "一般",
        "strategic_importance": "一般",
        "recommendations": []
    }
    
    # 经济状况分析
    if region.tax_income > 400000:
        analysis["economic_status"] = "富裕"
    elif region.tax_income > 200000:
        analysis["economic_status"] = "较好"
    elif region.tax_income < 100000:
        analysis["economic_status"] = "贫困"
    
    # 安全状况分析
    if region.stability >= 0.8:
        analysis["security_status"] = "稳定"
    elif region.stability >= 0.6:
        analysis["security_status"] = "较稳定"
    elif region.stability < 0.5:
        analysis["security_status"] = "不稳定"
    
    # 战略重要性分析
    if region.type in ["京师", "留都"] or "边关" in region.type:
        analysis["strategic_importance"] = "极其重要"
    elif region.tax_income > 300000 or region.population > 2000000:
        analysis["strategic_importance"] = "重要"
    
    # 建议
    if region.stability < 0.5:
        analysis["recommendations"].append("需要加强治安管理")
    if region.military_presence < region.population / 100:
        analysis["recommendations"].append("建议增加军事力量")
    if region.tax_income < region.population / 10:
        analysis["recommendations"].append("可以考虑发展经济")
    
    return analysis
