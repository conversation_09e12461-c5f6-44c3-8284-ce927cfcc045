#!/usr/bin/env python3
"""
测试前端日志功能
"""

from logger import log_ui_event, ui_logger

def test_logging():
    """测试日志功能"""
    print("🧪 测试前端日志系统...")
    
    # 测试不同类型的日志
    log_ui_event('info', '前端启动')
    log_ui_event('api', 'API请求测试', {'url': '/api/test', 'method': 'GET'})
    log_ui_event('auth', '用户登录', {'username': 'test_user'})
    log_ui_event('nav', '页面导航', {'from': '登录页', 'to': '主界面'})
    log_ui_event('chat', '大臣对话', {'minister': '温体仁', 'message': '测试消息'})
    log_ui_event('edict', '诏书操作', {'action': '提交', 'length': 100})
    log_ui_event('success', '操作成功')
    log_ui_event('warning', '警告信息')
    log_ui_event('error', '错误信息')
    
    print("✅ 日志测试完成")

if __name__ == "__main__":
    test_logging()
