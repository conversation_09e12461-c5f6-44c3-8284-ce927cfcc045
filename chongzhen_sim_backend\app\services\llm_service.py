"""
大语言模型服务
"""

import asyncio
from typing import List, Dict, Any, Optional
from openai import OpenAI
from app.config import settings
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class LLMService:
    """大语言模型服务类"""
    
    def __init__(self):
        """初始化LLM服务"""
        self.client = OpenAI(
            base_url=settings.OPENAI_BASE_URL,
            api_key=settings.OPENAI_API_KEY
        )
        self.model = settings.DEFAULT_MODEL
        self.max_tokens = settings.LLM_MAX_TOKENS
        self.temperature = settings.LLM_TEMPERATURE
        
    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """
        异步聊天完成
        
        Args:
            messages: 对话消息列表
            system_prompt: 系统提示词
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            AI回复内容
        """
        try:
            # 构建完整的消息列表
            full_messages = []

            # 添加系统提示词
            if system_prompt:
                full_messages.append({
                    "role": "system",
                    "content": system_prompt
                })

            # 添加对话消息
            full_messages.extend(messages)

            # 在线程池中执行同步调用
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.chat.completions.create(
                    model=self.model,
                    messages=full_messages,
                    temperature=temperature or self.temperature,
                    max_tokens=max_tokens or self.max_tokens
                )
            )

            # 提取回复内容
            content = response.choices[0].message.content

            logger.info(f"LLM响应成功，token使用: {response.usage.total_tokens}")
            return content

        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            # 返回默认回复
            return "抱歉，我现在无法回应您的话语。"
    
    def sync_chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """
        同步聊天完成（用于非异步环境）
        
        Args:
            messages: 对话消息列表
            system_prompt: 系统提示词
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            AI回复内容
        """
        try:
            # 构建完整的消息列表
            full_messages = []
            
            # 添加系统提示词
            if system_prompt:
                full_messages.append({
                    "role": "system",
                    "content": system_prompt
                })
            
            # 添加对话消息
            full_messages.extend(messages)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=full_messages,
                temperature=temperature or self.temperature,
                max_tokens=max_tokens or self.max_tokens
            )
            
            # 提取回复内容
            content = response.choices[0].message.content
            
            logger.info(f"LLM响应成功，token使用: {response.usage.total_tokens}")
            return content
            
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            # 返回默认回复
            return "抱歉，我现在无法回应您的话语。"



# 全局LLM服务实例
llm_service = LLMService()
