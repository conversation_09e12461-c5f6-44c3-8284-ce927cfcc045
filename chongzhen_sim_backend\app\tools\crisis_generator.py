"""
危机生成工具 - 生成和管理危机链条
"""

import random
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from app.tools.registry import tool
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class Crisis(BaseModel):
    """危机模型"""
    id: str
    type: str
    name: str
    description: str
    severity: int  # 1-10
    duration: int  # 持续回合数
    triggers: List[str]
    effects: Dict[str, Any]
    resolution_conditions: List[str]

class CrisisChain(BaseModel):
    """危机链条"""
    id: str
    crises: List[Crisis]
    current_crisis_index: int = 0
    is_active: bool = True

@tool("crisis_generator", "生成和管理危机事件")
async def generate_crisis(
    crisis_type: str = None,
    severity: int = None,
    current_situation: Dict[str, Any] = None
) -> Crisis:
    """生成危机事件"""
    
    # 预定义的危机模板
    crisis_templates = {
        "natural_disaster": [
            {
                "name": "大旱",
                "description": "连月无雨，田地龟裂，民不聊生",
                "base_severity": 6,
                "effects": {
                    "food_production": -0.3,
                    "population_happiness": -0.2,
                    "tax_income": -0.15
                }
            },
            {
                "name": "洪灾",
                "description": "河水暴涨，淹没良田，灾民四散",
                "base_severity": 7,
                "effects": {
                    "food_production": -0.4,
                    "population_happiness": -0.25,
                    "infrastructure": -0.2
                }
            },
            {
                "name": "蝗灾",
                "description": "蝗虫遮天蔽日，所过之处寸草不生",
                "base_severity": 8,
                "effects": {
                    "food_production": -0.5,
                    "population_happiness": -0.3,
                    "tax_income": -0.2
                }
            }
        ],
        "military_crisis": [
            {
                "name": "边军哗变",
                "description": "边军因欠饷数月，愤而哗变",
                "base_severity": 7,
                "effects": {
                    "military_morale": -0.3,
                    "border_defense": -0.4,
                    "treasury": -0.1
                }
            },
            {
                "name": "后金入侵",
                "description": "后金大军南下，边关告急",
                "base_severity": 9,
                "effects": {
                    "military_morale": -0.2,
                    "border_defense": -0.5,
                    "population_happiness": -0.2
                }
            }
        ],
        "political_crisis": [
            {
                "name": "党争激化",
                "description": "朝堂党争日益激烈，政令难行",
                "base_severity": 5,
                "effects": {
                    "administrative_efficiency": -0.3,
                    "official_loyalty": -0.2,
                    "policy_effectiveness": -0.25
                }
            },
            {
                "name": "贪腐案发",
                "description": "重要官员贪腐案发，朝野震动",
                "base_severity": 6,
                "effects": {
                    "official_loyalty": -0.25,
                    "population_trust": -0.2,
                    "treasury": -0.15
                }
            }
        ],
        "economic_crisis": [
            {
                "name": "银价暴跌",
                "description": "海外白银大量涌入，银价暴跌，经济混乱",
                "base_severity": 6,
                "effects": {
                    "treasury_value": -0.3,
                    "trade_income": -0.2,
                    "merchant_satisfaction": -0.25
                }
            }
        ]
    }
    
    # 选择危机类型
    if not crisis_type:
        crisis_type = random.choice(list(crisis_templates.keys()))
    
    # 选择具体危机
    available_crises = crisis_templates.get(crisis_type, [])
    if not available_crises:
        raise ValueError(f"未知的危机类型: {crisis_type}")
    
    crisis_template = random.choice(available_crises)
    
    # 计算最终严重程度
    final_severity = severity or crisis_template["base_severity"]
    if current_situation:
        # 根据当前情况调整严重程度
        if current_situation.get("stability", 0.5) < 0.3:
            final_severity += 1
        if current_situation.get("treasury", 0.5) < 0.2:
            final_severity += 1
    
    final_severity = min(10, max(1, final_severity))
    
    # 生成危机
    crisis = Crisis(
        id=f"crisis_{random.randint(1000, 9999)}",
        type=crisis_type,
        name=crisis_template["name"],
        description=crisis_template["description"],
        severity=final_severity,
        duration=random.randint(2, 6),  # 2-6回合
        triggers=[],
        effects=crisis_template["effects"],
        resolution_conditions=_generate_resolution_conditions(crisis_type, final_severity)
    )
    
    logger.info(f"生成危机: {crisis.name} (严重程度: {final_severity})")
    return crisis

@tool("crisis_chain_manager", "管理危机链条")
async def manage_crisis_chain(
    chain_id: str,
    action: str,
    crisis_data: Dict[str, Any] = None
) -> Dict[str, Any]:
    """管理危机链条"""
    
    # 这里应该与数据库交互，暂时返回模拟数据
    if action == "create":
        chain = CrisisChain(
            id=chain_id,
            crises=[],
            current_crisis_index=0,
            is_active=True
        )
        return {"status": "created", "chain": chain.dict()}
    
    elif action == "add_crisis":
        if crisis_data:
            crisis = Crisis(**crisis_data)
            # 添加到链条
            return {"status": "crisis_added", "crisis_id": crisis.id}
    
    elif action == "resolve":
        # 解决当前危机
        return {"status": "resolved", "next_crisis": None}
    
    return {"status": "unknown_action"}

def _generate_resolution_conditions(crisis_type: str, severity: int) -> List[str]:
    """生成解决条件"""
    base_conditions = {
        "natural_disaster": [
            "投入大量银两赈灾",
            "调集粮食救济灾民",
            "派遣官员安抚民心"
        ],
        "military_crisis": [
            "补发拖欠军饷",
            "派遣可信将领安抚",
            "调集援军平定叛乱"
        ],
        "political_crisis": [
            "严厉处置相关官员",
            "改革相关制度",
            "加强监察力度"
        ],
        "economic_crisis": [
            "调整货币政策",
            "加强市场监管",
            "扶持受影响行业"
        ]
    }
    
    conditions = base_conditions.get(crisis_type, ["采取适当措施"])
    
    # 根据严重程度调整条件数量
    if severity >= 8:
        return conditions  # 高严重程度需要所有条件
    elif severity >= 5:
        return conditions[:2]  # 中等严重程度需要部分条件
    else:
        return conditions[:1]  # 低严重程度只需要一个条件

@tool("crisis_evaluator", "评估危机影响")
async def evaluate_crisis_impact(
    crisis: Dict[str, Any],
    current_state: Dict[str, Any],
    player_actions: List[str] = None
) -> Dict[str, Any]:
    """评估危机对帝国的影响"""
    
    impact_result = {
        "immediate_effects": {},
        "long_term_effects": {},
        "resolution_progress": 0.0,
        "escalation_risk": 0.0
    }
    
    # 计算直接影响
    crisis_effects = crisis.get("effects", {})
    for effect_type, effect_value in crisis_effects.items():
        current_value = current_state.get(effect_type, 0.5)
        new_value = current_value + effect_value
        impact_result["immediate_effects"][effect_type] = {
            "old_value": current_value,
            "new_value": max(0, min(1, new_value)),
            "change": effect_value
        }
    
    # 评估玩家行动的效果
    if player_actions:
        resolution_conditions = crisis.get("resolution_conditions", [])
        matched_conditions = 0
        
        for action in player_actions:
            for condition in resolution_conditions:
                if any(keyword in action.lower() for keyword in condition.lower().split()):
                    matched_conditions += 1
                    break
        
        impact_result["resolution_progress"] = min(1.0, matched_conditions / len(resolution_conditions))
    
    # 计算升级风险
    severity = crisis.get("severity", 5)
    base_escalation = severity / 10
    if impact_result["resolution_progress"] < 0.3:
        base_escalation += 0.2
    
    impact_result["escalation_risk"] = min(1.0, base_escalation)
    
    return impact_result
