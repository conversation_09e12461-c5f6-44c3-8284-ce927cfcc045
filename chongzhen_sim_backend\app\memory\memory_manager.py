"""
记忆管理器 - 管理游戏的长期记忆
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from app.memory.vector_store import create_vector_store, VectorStore
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class MemoryManager:
    """记忆管理器"""
    
    def __init__(self):
        self.vector_store: VectorStore = create_vector_store()
        self.memory_types = {
            "edict": "诏书记录",
            "event": "重要事件",
            "character": "人物互动",
            "crisis": "危机处理",
            "summary": "阶段总结"
        }
    
    async def store_memory(
        self,
        player_id: int,
        memory_type: str,
        content: str,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """存储记忆"""
        try:
            memory_doc = {
                "id": f"{player_id}_{memory_type}_{datetime.now().timestamp()}",
                "player_id": player_id,
                "memory_type": memory_type,
                "content": content,
                "metadata": metadata or {},
                "timestamp": datetime.now().isoformat(),
                "importance": self._calculate_importance(memory_type, content, metadata)
            }
            
            success = await self.vector_store.add_documents([memory_doc])
            if success:
                logger.info(f"存储记忆成功: 玩家{player_id}, 类型{memory_type}")
            
            return success
            
        except Exception as e:
            logger.error(f"存储记忆失败: {e}")
            return False
    
    async def retrieve_memories(
        self,
        player_id: int,
        query: str,
        memory_types: List[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """检索相关记忆"""
        try:
            # 构建搜索查询
            search_query = f"玩家{player_id} {query}"
            
            # 搜索相关记忆
            results = await self.vector_store.search(search_query, limit * 2)
            
            # 过滤结果
            filtered_results = []
            for result in results:
                # 检查玩家ID
                if result.get("player_id") != player_id:
                    continue
                
                # 检查记忆类型
                if memory_types and result.get("memory_type") not in memory_types:
                    continue
                
                filtered_results.append(result)
                
                if len(filtered_results) >= limit:
                    break
            
            logger.info(f"检索到 {len(filtered_results)} 条相关记忆")
            return filtered_results
            
        except Exception as e:
            logger.error(f"检索记忆失败: {e}")
            return []
    
    async def store_edict_memory(
        self,
        player_id: int,
        turn: int,
        year: int,
        edict_content: str,
        execution_result: str
    ) -> bool:
        """存储诏书记忆"""
        metadata = {
            "turn": turn,
            "year": year,
            "result_summary": execution_result[:200]  # 截取前200字符作为摘要
        }
        
        content = f"崇祯{year}年第{turn}月诏书：{edict_content}\n执行结果：{execution_result}"
        
        return await self.store_memory(
            player_id=player_id,
            memory_type="edict",
            content=content,
            metadata=metadata
        )
    
    async def store_event_memory(
        self,
        player_id: int,
        event_type: str,
        event_description: str,
        impact: Dict[str, Any] = None
    ) -> bool:
        """存储事件记忆"""
        metadata = {
            "event_type": event_type,
            "impact": impact or {}
        }
        
        return await self.store_memory(
            player_id=player_id,
            memory_type="event",
            content=event_description,
            metadata=metadata
        )
    
    async def store_character_interaction(
        self,
        player_id: int,
        character_name: str,
        interaction_content: str,
        loyalty_change: float = 0.0
    ) -> bool:
        """存储人物互动记忆"""
        metadata = {
            "character_name": character_name,
            "loyalty_change": loyalty_change
        }
        
        content = f"与{character_name}的对话：{interaction_content}"
        
        return await self.store_memory(
            player_id=player_id,
            memory_type="character",
            content=content,
            metadata=metadata
        )
    
    async def store_crisis_memory(
        self,
        player_id: int,
        crisis_name: str,
        crisis_description: str,
        resolution_method: str,
        outcome: str
    ) -> bool:
        """存储危机处理记忆"""
        metadata = {
            "crisis_name": crisis_name,
            "resolution_method": resolution_method,
            "outcome": outcome
        }
        
        content = f"危机：{crisis_name}\n描述：{crisis_description}\n处理方式：{resolution_method}\n结果：{outcome}"
        
        return await self.store_memory(
            player_id=player_id,
            memory_type="crisis",
            content=content,
            metadata=metadata
        )
    
    async def create_period_summary(
        self,
        player_id: int,
        start_year: int,
        end_year: int
    ) -> Optional[str]:
        """创建阶段总结"""
        try:
            # 检索这个时期的所有记忆
            query = f"崇祯{start_year}年到{end_year}年"
            memories = await self.retrieve_memories(
                player_id=player_id,
                query=query,
                limit=50
            )
            
            if not memories:
                return None
            
            # 按类型分组记忆
            grouped_memories = {}
            for memory in memories:
                memory_type = memory.get("memory_type", "unknown")
                if memory_type not in grouped_memories:
                    grouped_memories[memory_type] = []
                grouped_memories[memory_type].append(memory)
            
            # 生成总结
            summary_parts = [f"崇祯{start_year}年至{end_year}年总结："]
            
            for memory_type, type_memories in grouped_memories.items():
                type_name = self.memory_types.get(memory_type, memory_type)
                summary_parts.append(f"\n{type_name}：")
                
                for memory in type_memories[:5]:  # 每类最多5条
                    content = memory.get("content", "")[:100]  # 截取前100字符
                    summary_parts.append(f"- {content}")
            
            summary = "\n".join(summary_parts)
            
            # 存储总结
            await self.store_memory(
                player_id=player_id,
                memory_type="summary",
                content=summary,
                metadata={
                    "start_year": start_year,
                    "end_year": end_year,
                    "memory_count": len(memories)
                }
            )
            
            logger.info(f"创建阶段总结: 玩家{player_id}, {start_year}-{end_year}年")
            return summary
            
        except Exception as e:
            logger.error(f"创建阶段总结失败: {e}")
            return None
    
    async def get_context_for_agent(
        self,
        player_id: int,
        context_type: str,
        query: str = "",
        limit: int = 5
    ) -> str:
        """为Agent获取上下文信息"""
        try:
            # 根据上下文类型确定搜索的记忆类型
            memory_type_mapping = {
                "edict_refine": ["edict", "summary"],
                "simulate": ["edict", "event", "crisis", "summary"],
                "roleplay": ["character", "edict"],
                "story_progress": ["event", "crisis", "summary"]
            }
            
            memory_types = memory_type_mapping.get(context_type, ["summary"])
            
            # 检索相关记忆
            memories = await self.retrieve_memories(
                player_id=player_id,
                query=query,
                memory_types=memory_types,
                limit=limit
            )
            
            if not memories:
                return "暂无相关历史记录。"
            
            # 格式化上下文
            context_parts = ["相关历史记录："]
            for memory in memories:
                content = memory.get("content", "")
                timestamp = memory.get("timestamp", "")
                memory_type = memory.get("memory_type", "")
                
                context_parts.append(f"\n[{self.memory_types.get(memory_type, memory_type)}] {content[:200]}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"获取Agent上下文失败: {e}")
            return "获取历史记录失败。"
    
    def _calculate_importance(
        self,
        memory_type: str,
        content: str,
        metadata: Dict[str, Any] = None
    ) -> float:
        """计算记忆重要性"""
        base_importance = {
            "edict": 0.8,
            "event": 0.6,
            "character": 0.5,
            "crisis": 0.9,
            "summary": 0.7
        }
        
        importance = base_importance.get(memory_type, 0.5)
        
        # 根据内容调整重要性
        high_importance_keywords = ["危机", "战争", "叛乱", "灾害", "重大", "关键"]
        for keyword in high_importance_keywords:
            if keyword in content:
                importance += 0.1
                break
        
        # 根据元数据调整
        if metadata:
            if metadata.get("loyalty_change", 0) != 0:
                importance += 0.1
            
            if metadata.get("impact"):
                importance += 0.1
        
        return min(1.0, importance)
    
    async def cleanup_old_memories(
        self,
        player_id: int,
        keep_days: int = 365
    ) -> int:
        """清理旧记忆"""
        try:
            # 这里应该实现清理逻辑
            # 暂时返回0表示没有清理任何记忆
            logger.info(f"记忆清理功能待实现: 玩家{player_id}")
            return 0
            
        except Exception as e:
            logger.error(f"清理记忆失败: {e}")
            return 0
