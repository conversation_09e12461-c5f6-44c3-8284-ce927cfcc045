# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库
pymongo==4.6.0
motor==3.3.2

# 数据验证
pydantic==2.5.0
pydantic[email]==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP客户端
aiohttp==3.9.1
httpx==0.25.2

# AI/LLM
openai==1.3.0

# 密码加密
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# 环境变量
python-dotenv==1.0.0

# 日志
structlog==23.2.0

# 向量数据库（可选）
qdrant-client==1.6.9
# weaviate-client==3.25.3

# 数据处理
pandas==2.1.4
numpy==1.25.2

# 时间处理
python-dateutil==2.8.2

# JSON处理
orjson==3.9.10

# 异步支持
asyncio-mqtt==0.13.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# 生产环境
gunicorn==21.2.0

# CORS支持
python-multipart==0.0.6

# MongoDB是唯一支持的数据库
