"""
游戏核心API路由 - MongoDB版本
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, Any, Optional
from app.db.database import get_database
from app.services.turn_controller import NewTurnController, TurnProgressStatus
from app.utils.logger import setup_logger
from app.db.managers import player_manager
from datetime import datetime

logger = setup_logger(__name__)
router = APIRouter()

# 全局回合控制器实例
turn_controller = NewTurnController()

# # 没用到 先删除了
# # 存储回合进度的全局变量（生产环境应使用Redis等）
# turn_progress_storage: Dict[str, TurnProgressStatus] = {}

class EdictSubmitRequest(BaseModel):
    """诏书提交请求"""
    player_id: str
    edict_content: str

class TurnProgressResponse(BaseModel):
    """回合进度响应"""
    player_id: str
    progress: Optional[Dict[str, Any]] = None  # 临时改为字典
    is_completed: bool = False

@router.post("/edict/submit")
async def submit_edict_and_advance_turn(player_id: str, edict_content: str):
    """提交诏书并推进回合"""
    try:
        # 诏书记入player数据库的 edicts_history
        # 获取玩家信息
        player = await player_manager.get_player_by_id(player_id)
        if not player:
            raise HTTPException(status_code=404, detail="玩家不存在")

        # 记录诏书到历史记录
        if not hasattr(player, 'edicts_history') or player.edicts_history is None:
            player.edicts_history = {}

        # 将诏书内容记录到当前回合
        current_turn = player.current_turn
        player.edicts_history[current_turn] = edict_content

        # 更新统计信息
        player.increment_stat("edicts")
        player.last_active = datetime.now()

        # 保存到数据库
        update_success = await player_manager.update_player(player)
        if not update_success:
            logger.warning(f"更新玩家诏书历史失败，但继续执行回合推进")
        else:
            logger.info(f"成功记录诏书到玩家 {player_id} 的历史记录，回合: {current_turn}")

        logger.info(f"玩家 {player_id} 提交诏书并开始回合推进")
        
        # 执行新的回合演进
        result = await turn_controller.execute_new_turn(player_id, edict_content)

        # # 标记完成
        # if player_id in turn_progress_storage:
        #     turn_progress_storage[player_id].detailed_status = "回合推进完成"
        if result.success:
            logger.info(f"回合推进完成，玩家: {player_id}, 成功: {result.success}")

    except Exception as e:
        logger.error(f"诏书提交失败: {e}")
        raise HTTPException(status_code=500, detail="诏书提交失败")

# TODO 可能前端展示进度要用 不确定 先删除了  
# @router.get("/turn/progress/{player_id}")
# async def get_turn_progress(player_id: str) -> TurnProgressResponse:
#     """获取回合推进进度"""
#     try:
#         progress = turn_progress_storage.get(player_id)
#         is_completed = progress is not None and "完成" in progress.detailed_status

#         return TurnProgressResponse(
#             player_id=player_id,
#             progress=progress,
#             is_completed=is_completed
#         )

#     except Exception as e:
#         logger.error(f"获取回合进度失败: {e}")
#         raise HTTPException(status_code=500, detail="获取回合进度失败")

@router.post("/turn/force_advance/{player_id}")
async def force_advance_turn(player_id: str):
    """强制推进回合（管理员功能）"""
    try:
        logger.info(f"强制推进回合，玩家: {player_id}")

        # 使用空诏书强制推进
        result = await turn_controller.execute_new_turn(player_id, "维持现状，无特殊指令")

        return {
            "message": "强制回合推进完成",
            "success": result.success,
            "turn_number": result.turn_number,
            "year": result.year
        }

    except Exception as e:
        logger.error(f"强制推进回合失败: {e}")
        raise HTTPException(status_code=500, detail="强制推进回合失败")

# 删除冲突的旧路由

# TODO 还没看这里

@router.get("/status/{player_id}")
async def get_game_status(player_id: str):
    """获取游戏状态"""
    try:
        # TODO: 实现游戏状态获取逻辑
        return {
            "player_id": player_id,
            "current_turn": 1,
            "current_year": 1,
            "status": "active"
        }
    except Exception as e:
        logger.error(f"获取游戏状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取游戏状态失败")

@router.get("/history/{player_id}")
async def get_game_history(player_id: str, limit: int = 10):
    """获取游戏历史"""
    try:
        from app.db.managers import player_manager

        # 获取玩家信息
        player = await player_manager.get_player_by_id(player_id)
        if not player:
            raise HTTPException(status_code=404, detail="玩家不存在")

        # 获取诏书历史
        edicts_history = player.edicts_history if hasattr(player, 'edicts_history') and player.edicts_history else {}

        # 转换为列表并按回合排序
        history_list = []
        for turn_str, edict_data in edicts_history.items():
            if isinstance(edict_data, dict):
                history_item = {
                    "turn": int(turn_str),
                    "content": edict_data.get("content", ""),
                    "submitted_at": edict_data.get("submitted_at", ""),
                    "year": edict_data.get("year", 1),
                    "status": edict_data.get("status", "unknown")
                }
                history_list.append(history_item)
            else:
                # 兼容旧格式（直接存储字符串）
                history_item = {
                    "turn": int(turn_str),
                    "content": str(edict_data),
                    "submitted_at": "",
                    "year": 1,
                    "status": "legacy"
                }
                history_list.append(history_item)

        # 按回合倒序排列，最新的在前面
        history_list.sort(key=lambda x: x["turn"], reverse=True)

        # 应用限制
        if limit > 0:
            history_list = history_list[:limit]

        return {
            "player_id": player_id,
            "edicts_history": history_list,
            "total_edicts": len(edicts_history),
            "current_turn": player.current_turn,
            "current_year": player.current_year
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取游戏历史失败: {e}")
        raise HTTPException(status_code=500, detail="获取游戏历史失败")

@router.post("/turn/advance/{player_id}")
async def advance_turn(player_id: str):
    """推进回合"""
    try:
        # TODO: 实现回合推进逻辑
        logger.info(f"玩家 {player_id} 推进回合")
        return {"message": "回合推进成功", "new_turn": 2}
    except Exception as e:
        logger.error(f"推进回合失败: {e}")
        raise HTTPException(status_code=500, detail="推进回合失败")

@router.get("/events/{player_id}")
async def get_current_events(player_id: str):
    """获取当前事件"""
    try:
        # TODO: 实现当前事件获取逻辑
        return {"events": []}
    except Exception as e:
        logger.error(f"获取当前事件失败: {e}")
        raise HTTPException(status_code=500, detail="获取当前事件失败")

@router.get("/ministers/{player_id}")
async def get_ministers(player_id: str):
    """获取大臣信息"""
    try:
        # TODO: 实现大臣信息获取逻辑
        return {"ministers": []}
    except Exception as e:
        logger.error(f"获取大臣信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取大臣信息失败")

@router.post("/minister/summon")
async def summon_minister(player_id: str, minister_name: str, topic: str):
    """召见大臣"""
    try:
        # TODO: 实现召见大臣逻辑
        logger.info(f"玩家 {player_id} 召见大臣 {minister_name}")
        return {"message": f"成功召见 {minister_name}", "response": "臣遵旨"}
    except Exception as e:
        logger.error(f"召见大臣失败: {e}")
        raise HTTPException(status_code=500, detail="召见大臣失败")

@router.get("/resources/{player_id}")
async def get_resources(player_id: str):
    """获取资源状况"""
    try:
        # TODO: 实现资源状况获取逻辑
        return {
            "treasury": 1000000,
            "food": 500000,
            "military": 100000,
            "population": 50000000
        }
    except Exception as e:
        logger.error(f"获取资源状况失败: {e}")
        raise HTTPException(status_code=500, detail="获取资源状况失败")

@router.get("/map/{player_id}")
async def get_map_info(player_id: str):
    """获取地图信息"""
    try:
        # TODO: 实现地图信息获取逻辑
        return {"regions": [], "total_regions": 0}
    except Exception as e:
        logger.error(f"获取地图信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取地图信息失败")
