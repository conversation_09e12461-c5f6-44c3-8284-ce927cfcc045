"""
推演主Agent - 负责根据玩家指令推演帝国变化
"""

from typing import Dict, Any, List
from pydantic import BaseModel
from app.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from app.tools.registry import get_tool
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class SimulateInput(AgentInput):
    """推演输入"""
    edict_content: str
    current_empire_state: Dict[str, Any]
    current_turn: int
    current_year: int

class SimulateOutput(BaseModel):
    """推演输出"""
    empire_changes: Dict[str, Any]
    events: List[Dict[str, Any]]
    narrative: str
    data_changes: Dict[str, Any]

class SimulateAgent(BaseAgent):
    """推演主Agent"""
    
    def get_system_prompt(self) -> str:
        return """你是明朝帝国的推演系统，负责根据皇帝的诏书推演帝国的变化。

你的职责：
1. 根据皇帝诏书的内容，推演对帝国各方面的影响
2. 考虑历史背景、当前局势、各方势力的反应
3. 生成合理的事件和变化
4. 计算数值变化（财政、军事、民心等）
5. 生成生动的叙事描述

推演要素：
- 军事：军队士气、战斗力、边防状况
- 财政：国库收支、税收、物价
- 民心：各地民心、官员忠诚度
- 外交：与周边势力关系
- 天灾人祸：自然灾害、疫病、叛乱

请基于历史真实性和逻辑合理性进行推演。"""

    async def process(self, input_data: SimulateInput) -> AgentOutput:
        """处理推演"""
        try:
            if not await self.validate_input(input_data):
                return self.create_error_output("输入数据无效")
            
            # 构建推演消息
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": self._build_simulate_prompt(input_data)}
            ]
            
            # 调用LLM进行推演
            response = await self.call_llm(messages, temperature=0.9, max_tokens=3000)
            
            # 解析推演结果
            simulate_result = self._parse_simulation_response(response)
            
            # 使用工具计算具体数值变化
            data_changes = await self._calculate_data_changes(
                input_data.edict_content, 
                input_data.current_empire_state
            )
            
            simulate_result.data_changes = data_changes
            
            logger.info(f"推演完成，玩家ID: {input_data.player_id}, 回合: {input_data.current_turn}")
            return self.create_success_output(simulate_result)
            
        except Exception as e:
            logger.error(f"推演失败: {e}")
            return self.create_error_output(f"推演失败: {str(e)}")
    
    def _build_simulate_prompt(self, input_data: SimulateInput) -> str:
        """构建推演提示"""
        empire_state_str = self.format_context(input_data.current_empire_state)
        
        return f"""
当前时间：崇祯{input_data.current_year}年第{input_data.current_turn}月

当前帝国状况：
{empire_state_str}

皇帝诏书内容：
{input_data.edict_content}

请根据以上信息进行推演，输出格式如下：

【帝国变化】
（描述诏书执行后帝国各方面的变化）

【重要事件】
（列出本月发生的重要事件）

【叙事描述】
（生动描述本月的情况，适合向玩家展示）

请确保推演符合历史背景和逻辑合理性。
"""
    
    def _parse_simulation_response(self, response: str) -> SimulateOutput:
        """解析推演响应"""
        # 简化的解析逻辑
        empire_changes = {}
        events = []
        narrative = response
        
        # 这里可以添加更复杂的解析逻辑
        # 提取具体的变化和事件
        
        return SimulateOutput(
            empire_changes=empire_changes,
            events=events,
            narrative=narrative,
            data_changes={}
        )
    
    async def _calculate_data_changes(
        self, 
        edict_content: str, 
        current_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算数值变化"""
        try:
            # 使用资源计算工具
            resource_calculator = get_tool("resource_calculator")
            if resource_calculator:
                return await resource_calculator.calculate_changes(
                    edict_content, current_state
                )
            return {}
        except Exception as e:
            logger.error(f"数值计算失败: {e}")
            return {}
