"""
诏书相关数据结构
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any

class EdictSubmission(BaseModel):
    """诏书提交"""
    content: str
    player_notes: Optional[str] = None

class EdictResponse(BaseModel):
    """诏书响应"""
    original_content: str
    refined_content: str
    status: str
    validation_errors: Optional[List[str]] = None

class EdictSection(BaseModel):
    """诏书分段"""
    military_section: str = ""
    domestic_section: str = ""
    financial_section: str = ""
    special_section: str = ""

class EdictValidation(BaseModel):
    """诏书验证结果"""
    is_valid: bool
    errors: List[str] = []
    warnings: List[str] = []

class EdictComplexity(BaseModel):
    """诏书复杂度分析"""
    overall_complexity: int
    section_complexity: Dict[str, int]
    execution_difficulty: str
    estimated_turns: int
    resource_requirements: Dict[str, Any]

class FormattedEdict(BaseModel):
    """格式化后的诏书"""
    sections: EdictSection
    validation: EdictValidation
    complexity: EdictComplexity
    raw_content: str

class EdictHistory(BaseModel):
    """诏书历史记录"""
    id: int
    turn: int
    year: int
    content: str
    execution_result: str
    created_at: str
